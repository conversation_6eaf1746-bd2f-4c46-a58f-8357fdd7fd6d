<template>
  <div v-if="pageType === 'list'" class="app-container">
    <div class="mainbox">
      <div class="filter-container">
        <el-form
          ref="queryForm"
          inline
          :model="queryParams"
          label-width="100px"
          @submit.native.prevent
        >
          <el-form-item label="培训名称">
            <el-input v-model="queryParams.trainName" />
          </el-form-item>

          <el-form-item label="培训类型">
            <el-select v-model="queryParams.trainType" clearable>
              <el-option
                v-for="item in trainingTypeOptions"
                :key="item.id"
                :value="item.dictCode"
                :label="item.dictName"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="培训时间">
            <el-date-picker
              v-model="trainingTime"
              type="datetimerange"
              start-placeholder="请选择"
              end-placeholder="请选择"
              value-format="yyyy-MM-dd HH:mm:ss"
              clearable
            />
          </el-form-item>

          <el-form-item label="培训方式">
            <el-select v-model="queryParams.online">
              <el-option
                v-for="item in trainingMethodOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="完成状态" width="80px" align="center">
            <el-select v-model="queryParams.finishStatus" clearable>
              <el-option
                v-for="item in finishStatusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>

          <div class="fr">
            <el-button type="primary" icon="el-icon-search" @click="handleQuery">
              搜索
            </el-button>

            <el-button icon="el-icon-refresh" @click="handleReset">
              重置
            </el-button>
          </div>
        </el-form>
      </div>

      <div class="table-container table-fullscreen">
        <div class="table-opt-container" />

        <el-table
          ref="table"
          v-loading="isLoading"
          style="width: 100%;"
          border
          highlight-current-row
          row-key="id"
          :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
          :default-expand-all="false"
          :header-cell-style="{ backgroundColor: '#f2f2f2' }"
          :data="tableData"
        >
          <template slot="empty">
            <p>{{ $store.getters.dataText }}</p>
          </template>

          <el-table-column
            type="index"
            label="序号"
            width="70"
            fixed="left"
            :index="
              (index) =>
                (queryParams.pageNo - 1) * queryParams.pageSize + index + 1
            "
          />

          <el-table-column
            key="trainName"
            label="培训名称"
            show-overflow-tooltip
            prop="trainName"
          />

          <el-table-column
            key="online"
            label="培训方式"
            prop="trainMethod"
            :formatter="trainingMethodFormatter"
            show-overflow-tooltip
            width="120"
          />

          <el-table-column
            label="培训类型"
            prop="trainTypeName"
            show-overflow-tooltip
            width="180"
          />

          <el-table-column
            label="培训时间"
            show-overflow-tooltip
            width="260"
            :formatter="trainingTimeFormatter"
          />

          <el-table-column
            label="是否重复"
            show-overflow-tooltip
            prop="repetition"
            :formatter="repetitionFormatter"
            width="110"
          />

          <el-table-column
            label="是否考试"
            show-overflow-tooltip
            prop="examType"
            :formatter="hasExamFormatter"
            width="110"
          />

          <el-table-column
            label="完成状态"
            prop="finishStatus"
            show-overflow-tooltip
            width="90"
          >
            <template #default="scope">
              <span>{{ scope.row.finishStatus === '1' ? '完成' : '未完成' }}</span>
            </template>
          </el-table-column>

          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
            fixed="right"
          >
            <template #default="scope">
              <el-button
                v-if="scope.row.evaluationStatus === '1'"
                type="text"
                @click="handleViewEvaluation(scope.row)"
              >
                查看评价
              </el-button>

              <el-button
                v-if="scope.row.finishStatus !== '1'"
                type="text"
                @click="handleParticipate(scope.row)"
              >
                参加
              </el-button>

              <el-button
                v-if="scope.row.finishStatus === '1' && scope.row.evaluationStatus === '0'"
                type="text"
                @click="handleTrainingEvaluate(scope.row)"
              >
                培训评价
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <dt-pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNo"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
    </div>
  </div>

  <AttendTraining
    v-else
    :training-plan-id="currentTrainingPlanId"
    :exam-type="examType"
    @back="handleBack"
  />
</template>

<script>
import { getMyTrainingList } from '@/api/training-examination/my-training'
import {
  TRAINING_METHOD_OPTIONS,
  CONSTANTS
} from '@/constants/training-plan'
import { FINISH_STATUS_OPTIONS } from '@/constants/my-training'
import dayjs from 'dayjs'
import AttendTraining from './components/attend-training'

export default {
  name: 'MyTraining',
  components: { AttendTraining },
  data() {
    return {
      // 常量对象
      pageType: 'list',
      CONSTANTS,
      currentTrainingPlanId: '',
      examType: 0,
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        trainName: '',
        online: '',
        trainType: '',
        finishStatus: '',
        searchBeginTime: '',
        searchEndTime: ''
      },

      // 培训方式
      trainingMethodOptions: TRAINING_METHOD_OPTIONS,
      // 完成状态
      finishStatusOptions: FINISH_STATUS_OPTIONS,

      total: 0,
      isLoading: false,
      tableData: [],
      trainingTypeOptions: []
    }
  },

  computed: {
    trainingTime: {
      set(value) {
        if (value && value.length === 2) {
          this.queryParams.searchBeginTime = value[0]
          this.queryParams.searchEndTime = value[1]
        } else {
          this.queryParams.searchBeginTime = ''
          this.queryParams.searchEndTime = ''
        }
      },

      get() {
        if (this.queryParams.searchBeginTime && this.queryParams.searchEndTime) {
          return [this.queryParams.searchBeginTime, this.queryParams.searchEndTime]
        } else {
          return []
        }
      }
    }
  },

  watch: {
    trainingVisible(visible) {
      if (!visible) {
        this.getList()
      }
    }
  },

  created() {
    this.handleQuery()
    this.getDicts()
  },

  mounted() {
  },

  methods: {
    handleBack() {
      this.pageType = 'list'
    },

    handleParticipate(row) {
      this.currentTrainingPlanId = row.trainPlanId
      this.examType = row.examType
      this.pageType = 'detail'
    },

    handleTrainingEvaluate(row) {
      this.$router.push({
        path: '/training-examination/training-evaluate',
        query: {
          id: row.id,
          trainName: row.trainName
        }
      })
    },

    handleViewEvaluation(row) {
      this.$router.push({
        path: '/training-examination/view-evaluation',
        query: {
          id: row.id,
          trainName: row.trainName
        }
      })
    },

    getDicts() {
      this.businessDictList({ dictTypeCode: 'trainType' }).then((res) => {
        this.trainingTypeOptions = res.data.rows
      })
    },

    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },

    handleReset() {
      this.queryParams = {
        orgId: this.$store.getters.orgId,
        planYear: `${dayjs().year()}`,
        trainType: '',
        completeStatus: '',
        delayStatus: '',
        searchBeginTime: '',
        searchEndTime: '',
        pageNo: 1,
        pageSize: 10
      }
      this.getList()
    },

    transformListData(listData) {
      if (!listData || listData.length === 0) {
        return []
      }
      return listData.map((item) => {
        if (item.repetitionList) {
          return {
            ...item,
            id: `group+${item.id}`,
            children: item.repetitionList
          }
        } else {
          return { ...item }
        }
      })
    },

    getList() {
      const loadData = (data) => {
        this.tableData = this.transformListData(data.data.rows)
        this.total = data.data.totalRows
      }

      this.isLoading = true
      getMyTrainingList(this.queryParams)
        .then(loadData)
        .finally(() => {
          this.isLoading = false
        })
    },

    trainingMethodFormatter(row, column, cellValue) {
      const item = this.trainingMethodOptions.find((item) => item.value === cellValue)
      return item ? item.label : cellValue
    },

    trainingTimeFormatter(row) {
      return `${row.trainBegin}~${row.trainEnd}`
    },

    isEnabledFormatter(row, column, cellValue) {
      const item = this.isEnabledOptions.find((item) => item.value === cellValue)
      return item ? item.label : cellValue
    },

    repetitionFormatter(row, column, cellValue) {
      return cellValue === CONSTANTS.REPETITION_NONE ? '不重复' : '重复'
    },

    hasExamFormatter(row, column, cellValue) {
      return cellValue === CONSTANTS.HAS_EXAM ? '是' : '否'
    },

    personnelScopeFormatter(row, column, cellValue) {
      return cellValue === CONSTANTS.PERSONNEL_SCOPE_EXTERNAL ? '外培' : '内培'
    }
  }
}
</script>

<style scoped lang="scss">
.view-text {
  color: var(--primary);
  cursor: pointer;
  font-weight: 500;
}
</style>
