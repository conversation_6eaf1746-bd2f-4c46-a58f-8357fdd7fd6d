<template>
  <div class="app-container">
    <div class="mainbox">
      <el-tabs v-model="queryParams.type" @tab-click="changeTab">
        <el-tab-pane label="自主学习" name="1"></el-tab-pane>
        <el-tab-pane label="答题练习" name="2"></el-tab-pane>
      </el-tabs>
      <div class="filter-container">
        <el-form :model="queryParams" ref="queryForm" :inline="true" @submit.native.prevent v-show="showSearch"
                 label-width="68px">
          <el-form-item :label="queryParams.type==='1'?'课程名称' :'练习名称'" prop="name">
            <el-input v-model.trim="queryParams.name" :placeholder="'请输入' + (queryParams.type==='1'?'课程名称' :'练习名称')" clearable
                      class="input-width"/>
          </el-form-item>
          <el-form-item v-if="queryParams.type==='1'" label="课程栏目" prop="classification">
            <el-select
              v-model="queryParams.classification"
              placeholder="请选择"
              style="width: 92%;"
              clearable
            >
              <el-option
                v-for="item in groupOptions"
                :key="item.dictCode"
                :label="item.dictName"
                :value="item.dictCode"
              />
            </el-select>
          </el-form-item>
        </el-form>
        <div class="flex-1"></div>
        <div>
          <div class="flex-1"></div>
          <div style="display:flex;">
            <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
          </div>
        </div>
      </div>
      <div class="table-container table-fullscreen">
        <div class="table-opt-container" style="text-align: right;">
          <el-button
            type="danger"
            :disabled="multiple"
            @click="changeStatus(2)"
          >停用
          </el-button>
          <el-button
            type="success"
            :disabled="multiple"
            @click="changeStatus(1)"
          >启用
          </el-button>
          <el-button
            type="primary"
            :disabled="multiple"
            @click="handleEdit(1)"
          >设置积分
          </el-button>
          <el-button
            type="primary"
            @click="handleEdit(2)"
          >积分上限
          </el-button>
          <div class="flex-1"></div>
          <dt-dialog-column v-model="isShowTable" :columns="showColumns" :table-ref="$refs.table"
                            @queryTable="getList"/>
        </div>
        <div class="table-opt-container" style="font-size: 20px">
          每日积分上限（分）：{{todayCanGet}}
        </div>
        <el-table v-if="isShowTable" v-loading="loading" ref="table" border highlight-current-row
                  :header-cell-style="{ backgroundColor: '#f2f2f2'}" :data="IntegrationTaskList"
                  @selection-change="handleSelectionChange">
          <template slot="empty">
            <p>{{ $store.getters.dataText }}</p>
          </template>
          <el-table-column fixed="left" type="selection" width="55" align="center"/>
          <el-table-column
            fixed="left"
            type="index"
            label="序号"
            width="70"
            :index="(index)=>(queryParams.pageNo - 1) * queryParams.pageSize + index + 1"
          />
          <el-table-column v-for="(item,index) in showColumns" :label="item.label" v-if="item.show"
                           show-overflow-tooltip :key="item.prop"
                           align="center">
            <template slot-scope="scope">
              <span v-if="item.prop === 'getType'">{{ scope.row.getType == 1 ? '及格' : scope.row.getType == 2? '满分' : '' }}</span>
              <span v-else-if="item.prop === 'status'">{{ scope.row.status == 1 ? '启用' : scope.row.status == 2 ? '停用' : '' }}</span>
              <span v-else>{{ scope.row[item.prop] }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" :key="Math.random()" fixed="right" align="center"
                           class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button
                size="small"
                @click="handleDetail(scope.row)"
              >查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <dt-pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNo"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
    </div>
    <!-- 添加或修改积分任务管理对话框 -->
    <dt-dialog :title="editType === 1? '设置积分': '设置积分上限'" :visible.sync="open" :is-loading="loading" @comfirmBtn="submitForm" @closeDialog="cancel">
      <div slot="content">
        <el-form ref="form" :model="form" :rules="rules" label-width="160px">
          <el-form-item label="任务名称" prop="courseType">
            {{queryParams.type === '1' ? '自主学习' : '答题练习'}}
          </el-form-item>
          <el-form-item v-if="editType === 1" :label="queryParams.type === '1' ? '课程名称' : '练习名称'" prop="name">
            {{ names }}
          </el-form-item>
          <el-form-item v-for="item in editColumns" :key="item.prop" :label="item.label" :prop="item.prop" :rules="item.rules">
            <el-select v-if="item.prop == 'getType'" v-model="form.getType" placeholder="请选择获取条件">
              <el-option label="及格" :value="1"/>
              <el-option label="满分" :value="2"/>
            </el-select>
            <el-input-number v-else v-model="form[item.prop]" :placeholder="'请输入'+ item.label" maxlength="6" :max="999999" :min="0" :precision="0"
                      show-word-limit class="limit"/>
          </el-form-item>
        </el-form>
      </div>
    </dt-dialog>
    <!-- 详情弹框 -->
    <dt-detail ref="dtDetailRef" :dt-detail-option="detailOption"/>
  </div>
</template>

<script>
import {
  listIntegrationTask,
  getIntegrationTask,
  delIntegrationTask,
  addIntegrationTask,
  updateIntegrationTask, getTodayIntegration, saveTodayIntegration
} from "@/api/training-examination/integration/IntegrationTask";

export default {
  name: "IntegrationTask",
  data() {
    return {
      // 遮罩层
      loading: true,
      //显隐表格
      isShowTable: true,
      // 选中数组
      ids: [],
      //列显隐数组
      showColumns: [
        {prop: "name", label: "课程名称", show: true},
        {prop: "courseType", label: "课程类型", show: true},
        {prop: "classification", label: "课程栏目", show: true},
        {prop: "courseScore", label: "课程分值", show: true},
        {prop: "times", label: "积分限制次数", show: true},
        {prop: "status", label: "状态", show: true}
      ],
      showColumns1: [
        {prop: "name", label: "课程名称", show: true},
        {prop: "courseType", label: "课程类型", show: true},
        {prop: "classification", label: "课程栏目", show: true},
        {prop: "courseScore", label: "课程分值", show: true},
        {prop: "times", label: "积分限制次数", show: true},
        {prop: "status", label: "状态", show: true}
      ],
      showColumns2: [
        {prop: "name", label: "练习名称", show: true},
        {prop: "classification", label: "题库栏目", show: true},
        {prop: "courseScore", label: "练习分值", show: true},
        {prop: "times", label: "积分限制次数", show: true},
        {prop: "getType", label: "获取条件", show: true},
        {prop: "status", label: "状态", show: true}
      ],
      editColumns: [
        {prop: "courseScore", label: "课程分值", rules: [{required: true, message: '请输入课程分值', trigger: 'blur'}]},
        {prop: "times", label: "积分限制次数", rules: []}
      ],
      editColumns1: [
        {prop: "courseScore", label: "课程分值", rules: [{required: true, message: '请输入课程分值', trigger: 'blur'}]},
        {prop: "times", label: "积分限制次数", rules: []}
      ],
      editColumns2: [
        {prop: "dailyLimit", label: "每日积分上限(分)", rules: [{required: true, message: '请输入每日积分上限', trigger: 'blur'}]}
      ],
      editColumns3: [
        {prop: "courseScore", label: "练习分值", rules: [{required: true, message: '请输入课程分值', trigger: 'blur'}]},
        {prop: "getType", label: "获取条件", rules: [{required: true, message: '请选择获取条件', trigger: 'change'}]},
        {prop: "times", label: "积分限制次数", rules: []}
      ],
      editColumns4: [
        {prop: "dailyLimit", label: "每日积分上限(分)", rules: [{required: true, message: '请输入每日积分上限', trigger: 'blur'}]}
      ],
      editType: 1,
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 积分任务管理表格数据
      IntegrationTaskList: [],
      groupOptions: [],
      // 弹出层标题
      title: "",
      names: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        type: '1',
        name: '',
        classification: ''
      },
      todayCanGet: 0,
      // 表单参数
      form: {},
      selection: [],
      // 表单校验
      rules: {}
    };
  },
  computed: {
    //详情参数
    detailOption() {
      return {
        labelWidth: '155px',
        rows: [],
        data: {}
      }
    },
  },
  created() {
    this.getList();
    this.getTodayIntegration()
    this.businessDictList({ dictTypeCode: 'courseGrouping' }).then((res) => {
      this.groupOptions = res.data.rows
    })
  },
  methods: {
    /** 查询积分任务管理列表 */
    getList() {
      this.loading = true;
      listIntegrationTask(this.queryParams).then(({data: response}) => {
        this.IntegrationTaskList = response.rows;
        this.IntegrationTaskList.forEach((item) => {
          item.courseScore = item.courseScore == null ? 0 : item.courseScore
          item.times = item.times == null ? 0 : item.times
          item.getType = item.getType == null ? 1 : item.getType
          item.dailyLimit = item.dailyLimit == null ? 0 : item.dailyLimit
          item.status = item.status == null ? 2 : item.status
        })
        this.total = response.totalRows;
        this.loading = false;
      });
    },
    getTodayIntegration() {
      getTodayIntegration(this.queryParams.type).then((response) => {
        this.todayCanGet = response.data.todayCanGet
      })
    },
    // 取消按钮
    cancel() {
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        mainId: null,
        type: null,
        courseScore: null,
        times: null,
        dailyLimit: null,
        getType: 1,
        status: null,
        createUser: null,
        createTime: null,
        updateUser: null,
        updateTime: null,
        delFlag: null,
        version: null,
        ownerOrgId: null,
        ownerUserId: null,
        ids: []
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    handleReset() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.mainId)
      this.names  = selection.map(item => item.name).join(',')
      this.single = selection.length !== 1
      this.selection = selection
      this.multiple = !selection.length
    },
    changeTab() {
      if (this.queryParams.type === '1'){
        this.showColumns = this.showColumns1
      }else {
        this.showColumns = this.showColumns2
      }
      this.getTodayIntegration()
      this.handleReset()
    },
    changeStatus(status){
      this.reset()
      // this.form.ids = this.ids
      // this.form.status = status
      // this.form.type = this.queryParams.type * 1
      let form = {
        ids: this.ids,
        status: status,
        type: this.queryParams.type * 1
      }
      addIntegrationTask(form).then(response => {
        this.$dtModal.msgSuccess("设置成功");
        this.open = false;
        this.getList();
      });
    },
    handleEdit(editType) {
      this.editType  = editType
      this.reset();
      this.form.dailyLimit  = this.todayCanGet
      if (!this.single){
        this.form.courseScore  = this.selection[0].courseScore
        this.form.times  = this.selection[0].times
        this.form.getType  = this.selection[0].getType != null ? this.selection[0].getType : 1
      }else {
        this.form.courseScore  = ''
        this.form.times  = ''
        this.form.getType  = 1
      }
      this.editColumns = this['editColumns'+((this.queryParams.type-1)*2+editType)]
      this.open = true;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getIntegrationTask(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改积分任务管理";
      });
    },
    handleDetail(row) {
      this.detailOption.rows = this['showColumns'+this.queryParams.type]
      this.detailOption.data = {
        id: row.id,
        name: row.name,
        classification: row.classification,
        courseType: row.courseType,
        courseScore: row.courseScore + '',
        times: row.times + '',
        getType: row.getType,
        status: row.status,
        createUser: row.createUser,
        createTime: row.createTime,
        updateUser: row.updateUser,
        updateTime: row.updateTime,
        delFlag: row.delFlag,
        version: row.version,
        ownerOrgId: row.ownerOrgId,
        ownerUserId: row.ownerUserId
      }
      this.detailOption.data.getType = this.detailOption.data.getType == 2? '满分' : '及格'
      this.detailOption.data.status = this.detailOption.data.status == 1? '启用' : '停用'
      this.$refs.dtDetailRef.dialogVisible = true
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.editType === 1){
            this.form.ids = this.ids
            this.form.type = this.queryParams.type * 1
            addIntegrationTask(this.form).then(response => {
              this.$dtModal.msgSuccess("设置成功");
              this.open = false;
              this.getList();
            });
          }else if (this.editType === 2){
            this.form.type = this.queryParams.type * 1
            saveTodayIntegration(this.form).then(response => {
              this.$dtModal.msgSuccess("修改成功");
              this.open = false;
              this.getTodayIntegration();
            });
          }

        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$dtModal.confirm('是否确认删除积分任务管理编号为"' + ids + '"的数据项？').then(function () {
        return delIntegrationTask(ids);
      }).then(() => {
        this.getList();
        this.$dtModal.msgSuccess("删除成功");
      }).catch(() => {
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('integrationTask/IntegrationTask/export', {
        ...this.queryParams
      }, `integrationTask_IntegrationTask.xlsx`)
    }
  }
};
</script>
