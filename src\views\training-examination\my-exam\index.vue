<template>
  <div class="app-container">
    <div class="mainbox">
      <div class="filter-container">
        <el-form
          ref="queryForm"
          :model="queryParams"
          :inline="true"
          label-width="70px"
          @submit.native.prevent
        >
          <el-form-item label="考试名称" prop="examName">
            <el-input
              v-model.trim="queryParams.examName"
              maxlength="30"
              clearable
              placeholder="请输入"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>

          <el-form-item label="考试时间">
            <el-date-picker
              v-model="queryParams.examTimeRange"
              type="daterange"
              value-format="yyyy-MM-dd"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            />
          </el-form-item>
        </el-form>

        <div class="flex-1" />

        <div class="fr">
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">
            搜索
          </el-button>

          <el-button icon="el-icon-refresh" @click="handleReset">
            重置
          </el-button>
        </div>
      </div>

      <div class="table-container table-fullscreen">
        <div class="table-opt-container">
          <div class="flex-1" />
        </div>

        <el-table
          ref="table"
          v-loading="loading"
          style="width: 100%;"
          border
          highlight-current-row
          :header-cell-style="{ backgroundColor: '#f2f2f2' }"
          :data="tableData"
        >
          <template slot="empty">
            <p>{{ $store.getters.dataText }}</p>
          </template>

          <el-table-column
            type="index"
            label="序号"
            width="70"
            :index="
              (index) =>
                (queryParams.pageNo - 1) * queryParams.pageSize + index + 1
            "
          />

          <el-table-column
            label="名称"
            show-overflow-tooltip
            align="center"
            prop="examName"
            min-width="260"
          />

          <el-table-column
            label="考试方式"
            show-overflow-tooltip
            align="center"
            prop="examMode"
            min-width="100"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.examMode == 1">
                线上考试
              </span>

              <span v-if="scope.row.examMode == 2">
                线下考试
              </span>
            </template>
          </el-table-column>

          <el-table-column
            label="考试类型"
            show-overflow-tooltip
            align="center"
            prop="examTypeName"
            min-width="100"
          />

          <el-table-column
            label="开始时间"
            show-overflow-tooltip
            align="center"
            prop="examBeginTime"
            min-width="180"
          />

          <el-table-column
            label="结束时间"
            show-overflow-tooltip
            align="center"
            prop="examEndTime"
            min-width="180"
          />

          <el-table-column
            label="及格线"
            show-overflow-tooltip
            align="center"
            prop="passLine"
            min-width="100"
          />

          <el-table-column
            label="最后一次提交时间"
            show-overflow-tooltip
            align="center"
            prop="endTime"
            min-width="180"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.endTime == null || scope.row.examMode == 2">
                --
              </span>

              <span v-else>
                {{ scope.row.endTime }}
              </span>
            </template>
          </el-table-column>

          <el-table-column
            label="用时"
            show-overflow-tooltip
            align="center"
            prop="examTime"
            min-width="160"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.examTime == null || scope.row.examMode == 2">
                --
              </span>

              <span v-else>
                {{ getFormattedUseTime(scope.row.examTime) }}
              </span>
            </template>
          </el-table-column>

          <el-table-column
            label="答题次数"
            show-overflow-tooltip
            align="center"
            prop="examCount"
            min-width="100"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.examCount == null || scope.row.examMode == 2">
                --
              </span>

              <span v-else>
                {{ scope.row.examCount }}
              </span>
            </template>
          </el-table-column>

          <el-table-column
            label="限考次数"
            show-overflow-tooltip
            align="center"
            prop="limitTimes"
            min-width="100"
          />

          <el-table-column
            label="状态"
            show-overflow-tooltip
            align="center"
            prop="status"
            width="100"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.examMode == 2">
                --
              </span>

              <span v-else-if="scope.row.examCount == scope.row.limitTimes" style="color:#F56C6C">
                次数已满
              </span>

              <span v-else-if="scope.row.status == 0">
                未开始
              </span>

              <span v-else-if="scope.row.status == 1">
                进行中
              </span>

              <span v-else-if="scope.row.status == 2">
                已结束
              </span>
            </template>
          </el-table-column>

          <el-table-column
            label="操作"
            align="center"
            min-width="200"
          >
            <template slot-scope="scope">
              <el-button v-if="scope.row.examMode == 1 && (scope.row.status == 1 || scope.row.status == 0)" type="text" @click="goExam(scope.row)">
                {{ scope.row.isContinue > 0 ? '继续答题' : '立即答题' }}
              </el-button>

              <el-button v-if="scope.row.examMode == 1 && scope.row.examCount > 0" type="text" @click="showResult(scope.row)">
                查看结果
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <dt-pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNo"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
    </div>

    <!-- 查看结果弹框 -->
    <PaperResultDialog ref="paperResultRef" />
  </div>
</template>

<script>
import { getFormattedUseTime } from '@/utils/paper'
import {
  getMyExamVOList,
  getIsSubmit
} from '@/api/training-examination/my-exam'
import PaperResultDialog from '@/views/training-examination/components/paper-result-dialog.vue'

export default {
  name: 'MyExam',
  components: { PaperResultDialog },

  data() {
    return {
      // 表格
      loading:false,
      tableData:[],
      total:0,
      queryParams:{
        examName:'',
        examTimeRange:[],
        examBeginTime:'',
        examEndTime:'',
        pageNo:1,
        pageSize:10
      }
    }
  },


  created() {
    this.handleQuery()
  },

  methods: {
    getFormattedUseTime,

    // 查询
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },

    // 重置
    handleReset() {
      this.queryParams = {
        examName:'',
        examTimeRange:[],
        examBeginTime:'',
        examEndTime:'',
        pageNo:1,
        pageSize:10
      }
      this.getList()
    },

    // 获取列表数据
    getList() {
      if (!!this.queryParams.examTimeRange && this.queryParams.examTimeRange.length > 0) {
        this.queryParams.examBeginTime = `${this.queryParams.examTimeRange[0]} 00:00:00`
        this.queryParams.examEndTime = `${this.queryParams.examTimeRange[1]} 23:59:59`
      } else {
        this.queryParams.examBeginTime = ''
        this.queryParams.examEndTime = ''
      }

      this.loading = true
      getMyExamVOList(this.queryParams).then((data) => {
        this.loading = false
        this.tableData = data.data.rows
        this.total = data.data.totalRows
      }).catch(() => {
        this.loading = false
      })
    },

    // 立即答题
    goExam(row) {
      if (row.status == 0) {
        this.$dtModal
          .confirm(`${row.examName}尚未开始，请耐心等待。`)
          .then(() => {})
          .catch(() => {})
      } else {
        getIsSubmit({ examPlanId: row.examPlanId }).then((res) => {
          const isSubmit = res.data.isSubmit
          if (isSubmit == 1) {
            if (row.isContinue > 0) { // 已提交但状态为继续
              this.getList()
            }
            this.$dtModal
              .confirm(`${row.isContinue > 0 ? '上次考试已结束，即将重新开始考试' : '即将开始考试'}！`)
              .then(() => {
                //  进入“参加考试”页面
                this.$router.push({
                  path: '/trainingExamination/takeExam',
                  query: {
                    id:row.examPlanId,
                    trainId:row.trainId,
                    isContinue:row.isContinue,
                    examName:encodeURIComponent(row.examName)
                  }
                })
              })
              .catch(() => {})
          } else {
            this.$dtModal
              .confirm('即将继续答题！')
              .then(() => {
                //  进入“参加考试”页面
                this.$router.push({
                  path: '/trainingExamination/takeExam',
                  query: {
                    id:row.examPlanId,
                    trainId:row.trainId,
                    isContinue:row.isContinue,
                    examName:encodeURIComponent(row.examName)
                  }
                })
              })
              .catch(() => {})
          }
        })
      }
    },

    // 查看结果
    showResult(row) {
      this.$refs.paperResultRef.init(JSON.parse(JSON.stringify(row)))
    }
  }
}
</script>

