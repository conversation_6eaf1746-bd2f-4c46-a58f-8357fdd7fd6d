import request, { cloud } from '@/framework/utils/request'

// 我的练习查询
export function getMyExerciseVOList(query) {
  return request({
    url: `${cloud.dqbasic}/trainingExercisePaper/getMyExerciseVOList`,
    method: 'get',
    params: query
  })
}

// 练习结果查询
export function getTrainingExercisePaperPage(query) {
  return request({
    url: `${cloud.dqbasic}/trainingExercisePaper/page`,
    method: 'get',
    params: query
  })
}

// 答题详情查询
export function getPaperQuestionPage(query) {
  return request({
    url: `${cloud.dqbasic}/trainingExercisePaper/paperQuestionPage`,
    method: 'get',
    params: query
  })
}

// 练习-开始答题前校验
export function trainingExerciseCheck(query) {
  return request({
    url: `${cloud.dqbasic}/trainingExercisePaper/addCheck`,
    method: 'get',
    params: query
  })
}

// 练习-开始答题
export function trainingExercisePaperAdd(query) {
  return request({
    url: `${cloud.dqbasic}/trainingExercisePaper/add`,
    method: 'get',
    params: query
  })
}

// 练习-交卷
export function commitPaperAndScore(data) {
  return request({
    url: `${cloud.dqbasic}/trainingExercisePaper/commitPaperAndScore`,
    method: 'post',
    data
  })
}
