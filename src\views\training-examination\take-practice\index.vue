<template>
  <div class="app-container">
    <div v-loading="loading" class="mainbox">
      <div class="exam-title header">
        <div>{{ decodeURIComponent($route.query.exerciseName) }}</div>
      </div>

      <div class="exam-container">
        <div v-if="examPaperSingle.length > 0" class="exam-single">
          <div class="exam-title">
            单选题（每题{{ examPaperSingle[0].score }}分）
          </div>

          <div v-for="(item,index) in examPaperSingle" :key="item.questionId" class="exam-item">
            <div class="content">
              <span>{{ (index + 1) + '、' }}</span>

              <div>{{ item.content }}</div>
            </div>

            <el-radio-group v-model="examPaperSingle[index].userAnswer">
              <el-radio
                v-if="item.optionA"
                label="0"
              >
                <span>{{ 'A、' + item.optionA }}</span>
              </el-radio>

              <el-radio
                v-if="item.optionB"
                label="1"
              >
                <span>{{ 'B、' + item.optionB }}</span>
              </el-radio>

              <el-radio
                v-if="item.optionC"
                label="2"
              >
                <span>{{ 'C、' + item.optionC }}</span>
              </el-radio>

              <el-radio
                v-if="item.optionD"
                label="3"
              >
                <span>{{ 'D、' + item.optionD }}</span>
              </el-radio>

              <el-radio
                v-if="item.optionE"
                label="4"
              >
                <span>{{ 'E、' + item.optionE }}</span>
              </el-radio>

              <el-radio
                v-if="item.optionF"
                label="5"
              >
                <span>{{ 'F、' + item.optionF }}</span>
              </el-radio>
            </el-radio-group>
          </div>
        </div>

        <div v-if="examPaperMultiple.length > 0" class="exam-multiple">
          <div class="exam-title">
            多选题（每题{{ examPaperMultiple[0].score }}分）
          </div>

          <div v-for="(item,index) in examPaperMultiple" :key="item.questionId" class="exam-item">
            <div class="content">
              <span>{{ (examPaperSingle.length + index + 1) + '、' }}</span>

              <div>{{ item.content }}</div>
            </div>

            <el-checkbox-group v-model="examPaperMultiple[index].userAnswer">
              <el-checkbox
                v-if="item.optionA"
                label="0"
              >
                <span>{{ 'A、' + item.optionA }}</span>
              </el-checkbox>

              <el-checkbox
                v-if="item.optionB"
                label="1"
              >
                <span>{{ 'B、' + item.optionB }}</span>
              </el-checkbox>

              <el-checkbox
                v-if="item.optionC"
                label="2"
              >
                <span>{{ 'C、' + item.optionC }}</span>
              </el-checkbox>

              <el-checkbox
                v-if="item.optionD"
                label="3"
              >
                <span>{{ 'D、' + item.optionD }}</span>
              </el-checkbox>

              <el-checkbox
                v-if="item.optionE"
                label="4"
              >
                <span>{{ 'E、' + item.optionE }}</span>
              </el-checkbox>

              <el-checkbox
                v-if="item.optionF"
                label="5"
              >
                <span>{{ 'F、' + item.optionF }}</span>
              </el-checkbox>
            </el-checkbox-group>
          </div>
        </div>

        <div v-if="examPaperJudge.length > 0" class="exam-judge">
          <div class="exam-title">
            判断题（每题{{ examPaperJudge[0].score }}分）
          </div>

          <div v-for="(item,index) in examPaperJudge" :key="item.questionId" class="exam-item">
            <div class="content">
              <span>{{ ( examPaperSingle.length + examPaperMultiple.length + index + 1) + '、' }}</span>

              <div>{{ item.content }}</div>
            </div>

            <el-radio-group v-model="examPaperJudge[index].userAnswer">
              <el-radio label="0">
                <span>正确</span>
              </el-radio>

              <el-radio label="1">
                <span>错误</span>
              </el-radio>
            </el-radio-group>
          </div>
        </div>
      </div>

      <div class="submit">
        <el-button type="primary" @click="submitPaper">
          交卷
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import {
  trainingExercisePaperAdd,
  commitPaperAndScore
} from '@/api/training-examination/my-practice.js'

export default {
  name: 'TakePractice',
  data() {
    return {
      loading:false,

      // 单选题组
      examPaperSingle:[],
      // 多选题组
      examPaperMultiple:[],
      // 判断题组
      examPaperJudge:[],

      paperId:'', // 试卷id
      startTime: '' // 开始时间
    }
  },

  created() {
    this.getExamPaperById()
  },

  methods: {
    // 获取试卷详情
    getExamPaperById() {
      this.loading = true
      trainingExercisePaperAdd({
        exerciseId:this.$route.query.id
      }).then((res) => {
        // 试卷id
        this.paperId = res.data.id
        // 开始练习时间
        this.startTime = res.data.startTime
        const questions = res.data.questions || []
        // 用户选择答案预留字段
        questions.map((item) => {
          if (item.quesType == '1') {
            item.userAnswer = []
          }
          return item
        })
        // 单选、多选、判断题过滤
        this.examPaperSingle = questions.filter((item) => item.quesType == 0)
        this.examPaperMultiple = questions.filter((item) => item.quesType == 1)
        this.examPaperJudge = questions.filter((item) => item.quesType == 2)
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },

    // 提交试卷
    submitPaper() {
      let examPaperMultiple = JSON.parse(JSON.stringify(this.examPaperMultiple))
      // 多选答案数组排序转字符串
      examPaperMultiple = examPaperMultiple.map((val) => {
        val.userAnswer.sort()
        val.userAnswer = val.userAnswer.toString()
        return val
      })
      const data = {
        id:this.paperId,
        startTime:this.startTime,
        questions:[...this.examPaperSingle, ...examPaperMultiple, ...this.examPaperJudge]
      }

      this.loading = true
      commitPaperAndScore(data).then((res) => {
        if (res.success == true) {
          this.$message({
            message: '练习结束！',
            type: 'success'
          })
          this.loading = false
          this.$store.dispatch('tagsView/delView', this.$route)
          this.$router.go(-1)
        }
      }).catch(() => {
        this.loading = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container{
  background: #fff;

  .mainbox{
    display: flex;
    flex-direction: column;
    align-items: center;

    .exam-title{
      width: 100%;
      height: 50px;
      background: #f2f2f2;
      padding: 0 20px;
      display: flex;
      align-items: center;
      color: #333;
      font-size: 16px;
      font-weight: 700;

      &.header{
        justify-content: center;
      }
    }

    .exam-container{
      margin-top: 20px;
      width: 96%;

      .exam-item{
        margin: 20px;

        .content{
          color: #333;
          font-size: 16px;
          display: flex;
          text-align: justify;
        }

        &:not(:last-of-type){
          border-bottom: 1px solid #eee;
        }
      }
    }

    .submit{
      margin-top: 40px;
      text-align: center;
    }
  }
}

.el-radio-group{
  display: flex;
  flex-direction: column;

  .el-radio{
    margin: 12px 0 12px 20px;
    white-space: normal;
    line-height: 20px;

  }
}

.el-checkbox-group{
  display: flex;
  flex-direction: column;

  .el-checkbox{
    margin: 12px 0 12px 20px;
    white-space: normal;
    line-height: 20px;
  }
}
</style>
