<!--
  * @Author: lize
  * @Date: 2025-05-20 15:13:11
  * @LastEditors: lize
  * @LastEditTime: 2025-05-20 15:13:11
  * @Description: 个人培训档案
  * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved.
-->
<template>
  <div class="app-container">
    <div class="mainbox">
      <div class="table-container">
        <div>
          <div style="font-size: 20px;">
            培训档案
          </div>
          <el-button type="primary" style="float: right;" @click="exportPreview">预览</el-button>
        </div>
        <el-tabs v-model="activeTab" tab-position="left">
          <!-- 基本信息 -->
          <el-tab-pane label="基本信息" name="basicInfo">
            <div class="train-type-label">
              <span>基本信息</span>
            </div>
            <el-form ref="form" :rules="rules" :model="form" style="margin-right: 65px;margin-left: 20px;">
              <el-row>
                <el-col :span="16">
                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="姓名" prop="baseInfo.name" label-width="160px">
                        <el-input v-model="form.baseInfo.name" placeholder="请输入姓名" :disabled="disabled"/>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="性别" prop="baseInfo.sex" label-width="160px">
                        <el-select v-model="form.baseInfo.sex" clearable style="width: 100%;" placeholder="请选择性别" :disabled="disabled">
                          <el-option label="男" value="M"></el-option>
                          <el-option label="女" value="F"></el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="年龄" prop="baseInfo.age" label-width="160px">
                        <el-input-number
                          v-model="form.baseInfo.age"
                          :controls="false"
                          class="train-archive-input-number"
                          placeholder="请输入年龄"
                          style="width: 100%;"
                          :disabled="disabled"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="文化程度" prop="baseInfo.education" label-width="160px">
                        <el-select v-model="form.baseInfo.education" clearable style="width: 100%;" :disabled="disabled">
                          <el-option
                            v-for="item of educationOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                          </el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="参加工作时间" prop="baseInfo.joinWorkTime" label-width="160px">
                        <el-date-picker
                          style="width: 100%;"
                          v-model="form.baseInfo.joinWorkTime"
                          type="date"
                          placeholder="选择日期"
                          format="yyyy-MM-dd"
                          value-format="yyyy-MM-dd"
                          :disabled="disabled">
                        </el-date-picker>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="调入工作时间" prop="baseInfo.adjustWorkTime" label-width="160px">
                        <el-date-picker
                          style="width: 100%;"
                          v-model="form.baseInfo.adjustWorkTime"
                          type="date"
                          placeholder="选择日期"
                          format="yyyy-MM-dd"
                          value-format="yyyy-MM-dd"
                          :disabled="disabled">
                        </el-date-picker>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="工种级别" prop="baseInfo.jobTypeLevel" label-width="160px">
                        <el-select v-model="form.baseInfo.jobTypeLevel" style="width: 100%;" clearable :disabled="disabled">
                          <el-option
                            v-for="item of jobTypeLevelOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                          </el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="原工种级别" prop="baseInfo.originJobTypeLevel" label-width="160px">
                        <el-select v-model="form.baseInfo.originJobTypeLevel" style="width: 100%;" clearable :disabled="disabled">
                          <el-option
                            v-for="item of jobTypeLevelOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                          </el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="从事本工种工作时间" prop="baseInfo.jobTypeWorkTime" label-width="160px">
                        <el-date-picker
                          style="width: 100%;"
                          v-model="form.baseInfo.jobTypeWorkTime"
                          type="date"
                          placeholder="选择日期"
                          format="yyyy-MM-dd"
                          value-format="yyyy-MM-dd"
                          :disabled="disabled">
                        </el-date-picker>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="工作单位" prop="baseInfo.deptId" label-width="160px">
                        <DeptSelect
                          v-model="form.baseInfo.deptId" placeholder="请选择工作单位"
                          :defalut-org-name="form.baseInfo.deptName"
                          :disabled="disabled"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-col>
                <el-col :span="8" style="text-align: center;">
                  <el-form-item label="" prop="imgId" label-width="0px">
                    <PictureUploadEcho
                      ref="pictureUploadEcho"
                      :file-type="['jpg', 'png']"
                      :file-limit="1"
                      :file-id.sync="form.baseInfo.imgId"
                      :disabled="disabled"
                    />
                    <div class="el-upload__tip">
                      照片（点击上传）
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
              <div>
                  <div style="display: flex;justify-content: space-between;">
                    <span style="font-size: 16px;">调岗记录</span>
                    <el-button v-if="!disabled" type="text" @click="handleAddRow">增行</el-button>
                  </div>
                  <div>
                    <el-table :data="form.baseInfo.jobTransferRecordList" border>
                      <el-table-column label="调转时间" prop="adjustTime" align="center">
                        <template slot-scope="scope">
                        <el-date-picker
                          style="width: 100%;"
                          v-model="scope.row.adjustTime"
                          type="date"
                          placeholder="选择日期"
                          format="yyyy-MM-dd"
                          value-format="yyyy-MM-dd"
                          :disabled="disabled">
                        </el-date-picker>
                        </template>
                      </el-table-column>
                      <el-table-column label="调转部门/班组" align="center" prop="adjustGroup">
                        <template slot-scope="scope">
                          <DeptSelect
                            v-model="scope.row.adjustGroup" placeholder="请选择调转部门/班组"
                            :defalut-org-name="scope.row.adjustGroupName"
                            :disabled="disabled"
                          />
                        </template>
                      </el-table-column>
                      <el-table-column v-if="!disabled" label="操作" align="center" prop="action" width="200px">
                        <template slot-scope="scope">
                          <el-button type="text" @click="handleDeleteRow(scope.$index)">删除</el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </div>
            </el-form>
          </el-tab-pane>

          <!-- 三级安全培训教育 -->
          <el-tab-pane label="三级安全培训教育" name="safetyTraining">
            <!-- 厂（公司）级安全教育 -->
            <div class="three-level-training">
              <div class="train-type-label">
                <span>厂（公司）级安全教育</span>
              </div>
              <div class="train-type-content">
                <div class="train-type-item">
                  <div class="train-type-item-label" style="min-width: 72px">教育内容：</div>
                  <div>
                    本单位安全生产情况及安全生产基本知识；本单位安全生产规章制度和劳动纪律；从业人员安全生产权利和义务；有关事故案例等。
                  </div>
                </div>
                <div class="train-type-item">
                  <div class="train-type-item-label">课程名称：</div>
                  <div>
                    {{ form.threeLevelTraining.companyLevel.online === '1' ? 
                      form.threeLevelTraining.companyLevel.courseNames: form.threeLevelTraining.companyLevel.courseFileName }}
                  </div>
                </div>
                <div class="train-type-item">
                  <div class="train-type-item-label">教育时间：</div>
                  <div>
                    {{ form.threeLevelTraining.companyLevel.online === '1' ? 
                      form.threeLevelTraining.companyLevel.trainBegin : form.threeLevelTraining.companyLevel.realBeginTime }}
                  </div>
                </div>
                <div class="train-type-item">
                  <div class="train-type-item-label">教育成绩：</div>
                  <div>
                    {{ form.threeLevelTraining.companyLevel.userExamScore }}
                  </div>
                </div>
                <div class="train-type-item">
                  <div class="train-type-item-label">教育人：</div>
                  <div>
                    {{ form.threeLevelTraining.companyLevel.teacherName }}
                  </div>
                </div>
                <div class="train-type-item">
                  <div class="train-type-item-label">受教育人（签名）：</div>
                  <div>
                    <PreviewImage style="display: inline-block; border-bottom: 1px solid #000; margin-right: 10px;"
                      v-if="form.threeLevelTraining.companyLevel.sign"
                      :file-id="form.threeLevelTraining.companyLevel.sign"
                      />
                    <div v-else class="sign-text-line"></div>
                    <el-button v-if="form.threeLevelTraining.companyLevel.trainId" @click="handleSign('companyLevel')">签字</el-button>
                  </div>
                </div>
              </div>
            </div>
            <!-- 车间（部门）级安全教育 -->
            <div class="three-level-training">
              <div class="train-type-label">
                <span>车间（部门）级安全教育</span>
              </div>
              <div class="train-type-content">
                <div class="train-type-item">
                  <div class="train-type-item-label" style="min-width: 72px">教育内容：</div>
                  <div>
                    工作环境及危险因素；所从事工种可能遭受的职业伤害和伤亡事故；所从事工种的安全职责、操作技能及强制性标准；自救互救、急救方法、疏散和现场紧急情况的处理；安全设备设施、个人防护用品的使用和维护；本车间（部门）安全生产状况及规章制度；预防事故和职业危害的措施及应注意的安全事项；有关事故案例等。
                  </div>
                </div>
                <div class="train-type-item">
                  <div class="train-type-item-label">课程名称：</div>
                  <div>
                    {{ form.threeLevelTraining.dptLevel.online === '1' ? 
                      form.threeLevelTraining.dptLevel.courseNames: form.threeLevelTraining.dptLevel.courseFileName }}
                  </div>
                </div>
                <div class="train-type-item">
                  <div class="train-type-item-label">分配车间（工段、区、队）日期：</div>
                  <div>
                    {{ form.threeLevelTraining.dptLevel.assignDate }}
                  </div>
                </div>
                <div class="train-type-item">
                  <div class="train-type-item-label">教育时间：</div>
                  <div>
                    {{ form.threeLevelTraining.dptLevel.online === '1' ? 
                      form.threeLevelTraining.dptLevel.trainBegin: form.threeLevelTraining.dptLevel.realBeginTime }}
                  </div>
                </div>
                <div class="train-type-item">
                  <div class="train-type-item-label">考试成绩：</div>
                  <div>
                    {{ form.threeLevelTraining.dptLevel.userExamScore }}
                  </div>
                </div>
                <div class="train-type-item">
                  <div class="train-type-item-label">教育人：</div>
                  <div>
                    {{ form.threeLevelTraining.dptLevel.teacherName }}
                  </div>
                </div>
                <div class="train-type-item">
                  <div class="train-type-item-label">受教育人（签名）：</div>
                  <div>
                    <PreviewImage style="display: inline-block; border-bottom: 1px solid #000; margin-right: 10px;"
                      v-if="form.threeLevelTraining.dptLevel.sign"
                      :file-id="form.threeLevelTraining.dptLevel.sign"
                      />
                    <div v-else class="sign-text-line"></div>
                    <el-button v-if="form.threeLevelTraining.dptLevel.trainId" @click="handleSign('dptLevel')">签字</el-button>
                  </div>
                </div>
              </div>
            </div>
            <!-- 班组级岗位安全教育 -->
            <div class="three-level-training">
              <div class="train-type-label">
                <span>班组级岗位安全教育</span>
              </div>
              <div class="train-type-content">
                <div class="train-type-item">
                  <div class="train-type-item-label" style="min-width: 72px">教育内容：</div>
                  <div>
                    岗位安全操作规程；岗位之间工作衔接配合的安全与职业卫生事项；有关事故案例；其他需要培训的内容等。
                  </div>
                </div>
                <div class="train-type-item">
                  <div class="train-type-item-label">课程名称：</div>
                  <div>
                    {{ form.threeLevelTraining.groupLevel.online === '1' ? 
                      form.threeLevelTraining.groupLevel.courseNames: form.threeLevelTraining.groupLevel.courseFileName }}
                  </div>
                </div>
                <div class="train-type-item">
                  <div class="train-type-item-label">分配班组日期：</div>
                  <div>
                    {{ form.threeLevelTraining.groupLevel.assignDate }}
                  </div>
                </div>
                <div class="train-type-item">
                  <div class="train-type-item-label">教育时间：</div>
                  <div>
                    {{ form.threeLevelTraining.groupLevel.online === '1' ? 
                    form.threeLevelTraining.groupLevel.trainBegin : form.threeLevelTraining.groupLevel.realBeginTime }}
                  </div>
                </div>
                <div class="train-type-item">
                  <div class="train-type-item-label">考试成绩：</div>
                  <div>
                    {{ form.threeLevelTraining.groupLevel.userExamScore }}
                  </div>
                </div>
                <div class="train-type-item">
                  <div class="train-type-item-label">教育人：</div>
                  <div>
                    {{ form.threeLevelTraining.groupLevel.teacherName }}
                  </div>
                </div>
                <div class="train-type-item">
                  <div class="train-type-item-label">受教育人（签名）：</div>
                  <div>
                    <PreviewImage style="display: inline-block; border-bottom: 1px solid #000; margin-right: 10px;"
                      v-if="form.threeLevelTraining.groupLevel.sign"
                      :file-id="form.threeLevelTraining.groupLevel.sign"
                      />
                    <div v-else class="sign-text-line"></div>
                    <el-button v-if="form.threeLevelTraining.groupLevel.trainId" @click="handleSign('groupLevel')">签字</el-button>
                  </div>
                </div>
                <div class="train-type-item">
                  <div class="train-type-item-label">包教师傅：</div>
                  <div>
                    <PersonSelect
                      ref="traineeSelectRef"
                      v-model="form.threeLevelTraining.groupLevel.groupMasterUserId"
                      :api="personSelectorApi"
                      :reappear="true"
                      :tag-visible="true"
                      :multiple="false"
                      :disabled="hasPushNoticeToMaster"
                      style="display: inline-block;margin-right: 20px;"
                      @hook:mounted="handleTraineeSelectMounted"
                    />
                    <PreviewImage style="display: inline-block; border-bottom: 1px solid #000; margin-right: 10px;"
                      v-if="form.threeLevelTraining.groupLevel.groupMasterSign"
                      :file-id="form.threeLevelTraining.groupLevel.groupMasterSign"
                    />
                    <div v-else class="sign-text-line"></div>
                  </div>
                </div>
                <div class="train-type-item">
                  <div class="train-type-item-label">独立操作前考试成绩：</div>
                  <div>
                    <el-input v-model="form.threeLevelTraining.groupLevel.groupMasterScore" disabled style="width: 200px;"></el-input>
                    <el-button 
                      v-if="!hasPushNoticeToMaster"
                      @click="handlePushNoticeToMaster">
                      师傅签字
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <!-- 日常培训 -->
          <el-tab-pane label="日常培训" name="dailyTraining">
            <div class="train-type-label">
              <span>日常职工安全教育培训记录卡</span>
            </div>
            <el-table :data="form.dailyTrainList" border>
              <el-table-column label="序号" type="index" width="80" align="center"/>
              <el-table-column label="培训时间" prop="trainingTime" align="center">
                <template slot-scope="scope">
                  {{ scope.row.online === '1' ? `${scope.row.trainBegin} - ${scope.row.trainEnd}` 
                  : `${scope.row.realBeginTime} - ${scope.row.realEndTime}` }}
                </template>
              </el-table-column>
              <el-table-column label="培训内容" prop="courseNames" align="center"/>
              <el-table-column label="考核情况" prop="userExamScore" align="center"/>
              <el-table-column label="本人签字" prop="sign" align="center">
                <template slot-scope="scope">
                  <PreviewImage style="display: inline-block;"
                      v-if="scope.row.sign"
                      :file-id="scope.row.sign"
                  />
                </template>
              </el-table-column>
              <el-table-column label="操作" prop="action" align="center">
                <template slot-scope="scope">
                  <el-button type="text" @click="handleSignDaily(scope.row)">签字</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>

          <!-- 调岗培训 -->
          <el-tab-pane label="调岗培训" name="jobTransferTraining">
            <div class="train-type-label">
              <span>调岗培训</span>
            </div>
            <el-table :data="form.jobTransferList" border>
              <el-table-column label="调动时间" align="center">
                <template slot-scope="scope">
                  <el-date-picker
                    style="width: 100%;"
                    v-model="scope.row.jobTransferTime"
                    type="date"
                    placeholder="选择日期"
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                    :disabled="disabled">
                  </el-date-picker>
                </template>
              </el-table-column>
              <el-table-column label="调入部门/班组" prop="jobTransferToDeptId" align="center">
                <template slot-scope="scope">
                  <DeptSelect
                    v-model="scope.row.jobTransferToDeptId" placeholder="请选择工作单位"
                    :defalut-org-name="scope.row.jobTransferToDeptName"
                    :disabled="disabled"
                  />
                </template>
              </el-table-column>
              <el-table-column label="教育内容" prop="courseNames" align="center">
                <template slot-scope="scope"> 
                  {{ scope.row.online === '1' ? scope.row.courseNames : scope.row.courseFileName }}
                </template>
              </el-table-column>
              <el-table-column label="讲课时间" prop="trainTime" align="center">
                <template slot-scope="scope">
                  {{ scope.row.online === '1' ? `${scope.row.trainBegin}` 
                  : `${scope.row.realBeginTime}` }}
                </template>
              </el-table-column>
              <el-table-column label="考试成绩" prop="userExamScore" align="center"/>
              <el-table-column label="负责人签字" prop="sign" align="center">
                <template slot-scope="scope">
                   <PreviewImage style="display: inline-block;"
                      v-if="scope.row.signature"
                      :file-id="scope.row.signature"
                  />
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
        <div style="display: flex;justify-content: center;margin-top: 20px;">
          <el-button v-if="action === 'add'" type="primary" :loading="saveLoading" @click="handleSave">
            保存
          </el-button>
          <el-button v-else-if="action === 'edit'" type="primary" :loading="saveLoading" @click="handleEdit">
            保存
          </el-button>
          <el-button v-else-if="action === 'detail'" type="primary" :loading="saveLoading" @click="action = 'edit'">
            编辑
          </el-button>
        </div>
      </div>
    </div>
    <el-dialog
      title="员工培训档案"
      :visible.sync="previewVisible"
      width="60%"
      :before-close="previewClose">
      <div style="text-align: right; padding-bottom: 10px;">
        <el-button type="primary" @click="exportDownload">
          下载
        </el-button>
      </div>
      <div class="document-wrapper">
        <VueOfficeDocx :src="fileData" />
      </div>
    </el-dialog>

    <el-dialog
      title="签字"
      :visible.sync="signVisible"
      width="308px"
      class="archive-sign-dialog"
    >
      <div style="margin: 8px 0">
        <span>确认签字？</span>
      </div>
      <PreviewImage
        v-if="signImgId"
        :file-id="signImgId"
        class="confirm-sign-image"
      />
      <div slot="footer" class="dialog-footer">
        <el-button @click="signVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSignConfirm">确定</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import PictureUploadEcho from '@/components/picture-upload-echo/picture-upload-echo.vue'
import { getUserInfo, getCurrentInfo, addPersonalArchive,
  editPersonalArchive, getPersonalArchiveDetail, exportPreview, pushSignNoticeToMaster
 } from '@/api/training-examination/training-archive.js'
import DeptSelect from '@/components/dept-select/dept-select.vue'
import PreviewImage from '@/components/preview-image/preview-image.vue'
import {
  getManagingOrgUserList,
  searchUserName,
  getUserListByUserIds,
  searchOrgName
} from '@/framework/api/affairManage/flowDetail/Config'
import VueOfficeDocx from '@vue-office/docx'
export default {
  name: 'EmployeeArchive',
  components: { PictureUploadEcho, DeptSelect, PreviewImage, VueOfficeDocx },
  data() {
    return {
      activeTab: 'basicInfo',
      saveLoading: false,
      action: 'add',
      // 签字确认弹框是否显示
      signVisible: false,
      // 签字类型
      signType: '',
      // 表格签字行数据
      signRow: null,

      // 人员选择器API
      personSelectorApi: {
        getManagingOrgUserList,
        searchUserName,
        getUserListByUserIds,
        searchOrgName
      },

      form: {
        baseInfo: {
          // 姓名
          name: null,
          // 性别
          sex: null,
          // 年龄
          age: undefined,
          // 文化程度
          education: null,
          // 参加工作时间
          joinWorkTime: null,
          // 调入工作时间
          adjustWorkTime: null,
          // 工种级别
          jobTypeLevel: null,
          // 原工种级别
          originJobTypeLevel: null,
          // 从事本工种工作时间
          jobTypeWorkTime: null,
          // 工作单位
          deptId: null,
          // 照片
          imgId: null,
          // 调岗记录
          jobTransferRecordList: [],
        },


        // 三级安全培训教育
        threeLevelTraining: {
          // 厂（公司）级安全教育
          companyLevel: {
            // 培训计划id
            trainId: '',
            // 培训类型
            trainType: '',
            // 培训课程名称
            onlineCourseNames: '',
            // 培训时间
            trainDate: '',
            // 培训成绩
            userExamScore: '',
            // 培训教师名称
            teacherName: '',
            // 分配车间（工段、区、队）日期 / 分配班组日期
            assignDate: '',
            // 受教育人（签名）
            sign: '',
          },
          // 部门级安全教育
          dptLevel: {
            // 培训计划id
            trainId: '',
            // 培训类型
            trainType: '',
            // 培训课程名称
            onlineCourseNames: '',
            // 培训时间
            trainDate: '',
            // 培训成绩
            userExamScore: '',
            // 培训教师名称
            teacherName: '',
            // 分配车间（工段、区、队）日期 / 分配班组日期
            assignDate: '',
            // 受教育人（签名）
            sign: '',
          },
          // 班组级岗位安全教育
          groupLevel: {
            // 培训计划id
            trainId: '',
            // 培训类型
            trainType: '',
            // 培训课程名称
            onlineCourseNames: '',
            // 培训时间
            trainDate: '',
            // 培训成绩
            userExamScore: '',
            // 培训教师名称
            teacherName: '',
            // 分配车间（工段、区、队）日期 / 分配班组日期
            assignDate: '',
            // 受教育人（签名）
            sign: '',
            // 班组级-保教师傅-用户id
            groupMasterUserId: '',
            // 班组级-保教师傅-签字图片id
            groupMasterSign: '',
            // 班组级-保教师傅-打分分数
            groupMasterScore: ''
          },
        },
        // 日常培训记录
        dailyTrainList: [],
        // 调岗培训记录
        jobTransferList: [],
      },

      // 文化程度
      educationOptions: [
        { label: '小学', value: '1' },
        { label: '初中', value: '2' },
        { label: '高中', value: '3' },
        { label: '中专', value: '4' },
        { label: '职高', value: '5' },
        { label: '技校', value: '6' },
        { label: '大专', value: '7' },
        { label: '本科', value: '8' },
        { label: '硕士研究生', value: '9' },
        { label: '博士研究生', value: '10' },
      ],

      // 工种级别
      jobTypeLevelOptions: [
        { label: '初级', value: '1' },
        { label: '中级', value: '2' },
        { label: '高级', value: '3' },
        { label: '技师', value: '4' },
        { label: '高级技师', value: '5' },
      ],

      // 校验规则
      rules: {
        'baseInfo.name': [
            { required: true, message: '请输入姓名', trigger: 'blur' },
        ],
        'baseInfo.sex': [
            { required: true, message: '请选择性别', trigger: 'change' },
        ],
        'baseInfo.age': [
            { required: true, message: '请输入年龄', trigger: 'blur' },
        ],
        'baseInfo.education': [
            { required: true, message: '请选择文化程度', trigger: 'change' },
        ],
        'baseInfo.joinWorkTime': [
            { required: true, message: '请选择参加工作时间', trigger: 'change' },
        ],
        'baseInfo.adjustWorkTime': [
            { required: true, message: '请选择调入工作时间', trigger: 'change' },
        ],
        'baseInfo.jobTypeLevel': [
            { required: true, message: '请选择工种级别', trigger: 'change' },
        ],
        'baseInfo.originJobTypeLevel': [
            { required: true, message: '请选择原工种级别', trigger: 'change' },
        ],
        'baseInfo.jobTypeWorkTime': [
            { required: true, message: '请选择从事本工种工作时间', trigger: 'change' },
        ],
        'baseInfo.deptId': [
            { required: true, message: '请选择工作单位', trigger: 'change' },
        ],
      },
      // 签名图片id
      signImgId: null,
      fileData: null,
      previewVisible: false,
    }
  },

  created() {

  },

  watch: {
    // 监听签名图片id
    'form.baseInfo.imgId'(newVal) {
      if (newVal) {
        // 隐藏图片上传组件的上传按钮
        this.$refs.pictureUploadEcho.hideUploadEdit = true
      } else {
        // 显示图片上传组件的上传按钮
        this.$refs.pictureUploadEcho.hideUploadEdit = false
      }
    }
  },
  computed: {
    hasPushedMasterSign() {
      return this.form.threeLevelTraining.groupLevel.groupMasterSign
    },

    hasPushNoticeToMaster() {
      return this.form.threeLevelTraining.groupLevel.pushNoticeToMasterFlag === 'Y'
    },

    disabled() {
      return this.action === 'detail'
    }
  },
  mounted() {
    // 获取详情
    this.getDetail()
    // 获取签名图片id
    getCurrentInfo().then(res => {
      this.signImgId = res.data.sign
    })
  },

  methods: {
    // 基本信息-调岗记录-增行
    handleAddRow() {
      this.form.baseInfo.jobTransferRecordList.push({});
    },

    // 基本信息-调岗记录-删除行
    handleDeleteRow(index) {
      this.form.baseInfo.jobTransferRecordList.splice(index, 1);
    },

    // 签字
    handleSign(level) {
      this.signType = level;
      this.signVisible = true;
    },

    // 确认签字
    handleSignConfirm() {
      if (this.signImgId === '' || this.signImgId === null || this.signImgId === undefined) {
        this.$message({
          message: '请先在个人中心中设置本人签名！',
          type: 'error'
        });
        return;
      }
      if ('daily' !== this.signType) {
        this.$set(this.form.threeLevelTraining[this.signType], 'sign', this.signImgId);
      } else {
        this.$set(this.signRow, 'sign', this.signImgId);
      }
      this.signType = ''
      this.signVisible = false
      this.signRow = null;
    },

    // 日常培训-签字
    handleSignDaily(row) {
      this.signType = 'daily';
      this.signVisible = true;
      this.signRow = row;
    },

    // 保存
    handleSave() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.saveLoading = true;
          addPersonalArchive(this.form).then(res => {
            if (res.code === '00000') {
              this.$set(this.form.baseInfo, 'id', res.data.id);
              this.action = 'detail';
              this.$message({
                message: '保存成功',
                type: 'success'
              });
            }
          }).finally(() => {
            this.saveLoading = false;
          });
        } else {
          this.$message({
            message: '请填写完整信息',
            type: 'error'
          });
        }
      });
    },

    // 编辑
    handleEdit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.saveLoading = true;
          editPersonalArchive(this.form).then(res => {
            if (res.code === '00000') {
              this.action = 'detail';
              this.$message({
                message: '修改成功',
                type: 'success'
              });
            }
          }).finally(() => {
            this.saveLoading = false;
          });
        }
      });
    },

    // 获取详情
    getDetail() {
      getPersonalArchiveDetail().then(res => {
        if (res.code === '00000') {
          if (res.data && res.data.baseInfo && res.data.baseInfo.id) {
            this.action = 'detail';
          } else {
            this.action = 'add';
          }

          if (res.data) {
            if (res.data.baseInfo.id) {
              this.$set(this.form, 'baseInfo', res.data.baseInfo);
              if (res.data.baseInfo.jobTransferRecordList === null) {
                this.$set(this.form.baseInfo, 'jobTransferRecordList', []);
              }
            } else {
              this.$set(this.form.baseInfo, 'name', res.data.baseInfo.name);
              this.$set(this.form.baseInfo, 'sex', res.data.baseInfo.sex);
            }

            this.$set(this.form, 'threeLevelTraining', res.data.threeLevelTraining);
            this.$set(this.form, 'dailyTrainList', res.data.dailyTrainList);
            this.$set(this.form, 'jobTransferList', res.data.jobTransferList);
          }
        }
      })
    },

    // 加载用户信息并填充到表单中
    loadUserInfo() {
      getUserInfo().then(res => {
        this.$set(this.form.baseInfo, 'name', res.data.realName)
        this.$set(this.form.baseInfo, 'sex', res.data.sex)
      })
    },

    // 推送给包教师傅
    handlePushNoticeToMaster() {
      let list = this.$refs.traineeSelectRef.select
      if (list.length === 0) {
        this.$message({
          message: '请选择包教师傅',
          type: 'warning'
        });
        return;
      }
      let masterName = list[0].realName
      this.$confirm(`推送后，包教师傅将不能修改，确定推送给${masterName}进行签字？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        pushSignNoticeToMaster(this.form.threeLevelTraining.groupLevel.trainId).then(res => {
          if (res.code === '00000') {
            this.$message({
              message: '已推送给包教师傅',
              type: 'success'
            });
          }
        });
      }).catch(() => {
      });
    },
    handleTraineeSelectMounted() {
      this.$refs.traineeSelectRef.maxNum = 999999
    },

    // 导出预览
    exportPreview() {
      exportPreview(true).then(res => {
        this.previewVisible = true;
        this.fileData = res.data;
      })
    },
    // 下载
    exportDownload() {
      exportPreview(false).then(res => {
        // 创建blob对象
        const blob = new Blob([res.data], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' })
        // 创建下载链接
        const downloadUrl = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = downloadUrl
        // 从Content-Disposition中获取文件名，或使用固定文件名
        link.download = '职工安全教育培训档案.docx'

        document.body.appendChild(link)
        link.click()
        window.URL.revokeObjectURL(downloadUrl)
        document.body.removeChild(link)
      })
    },
    previewClose() {
      this.previewVisible = false
    },
  }
}
</script>

<style lang="scss" scoped>

.three-level-training{
  margin-left: 20px;
}

.three-level-training:not(:first-child){
  margin-top: 30px;
}

.train-type-label{
  font-size: 18px;
  margin-bottom: 10px;
}

.train-type-content{
  margin-left: 20px;
}
.train-type-item{
  display: flex;
  font-size: 14px;
  line-height: 30px;
}
.train-type-item-label{
  min-width: 72px
}
.sign-text-line{
  height: 0px;
  width: 130px;
  border-bottom: 1px solid rgb(0, 0, 0);
  display: inline-block;
  position: relative;
  top: 10px;
  margin-right: 15px;
}
.train-archive-input-number{
  ::v-deep{
    input{
      width: 100%;
      text-align: left;
    }
  }
}
.archive-sign-dialog{
  ::v-deep{
    .el-dialog__header{
      border-bottom: 1px solid #DCDFE6;
    }
  }
}
.confirm-sign-image{
  ::v-deep{
    .el-image{      
      background-color: #f2f2f2;
    }
  }
}
</style>
