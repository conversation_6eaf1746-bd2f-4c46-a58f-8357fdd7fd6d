<template>
  <div
    v-loading="isCourseLoading"
    class="training-course"
    :class="isForcedPaused ? 'player-paused' : ''"
  >
    <FilePreview
      v-if="isFilePreviewVisible"
      ref="filePreviewRef"
      :source="filePreviewUrl"
      height="100%"
      @iframe-loaded="handleIframeLoaded"
    />

    <AliyunPlayer
      v-if="isPlayerVisible"
      ref="playerRef"
      :source="videoPlaybackUrl"
      :max-playback-time.sync="videoMaxPlaybackTime"
      :config="playerConfig"
      :is-forward-seek-allowed="false"
      @player-ready="handlePlayerReady"
      @player-timeupdate="handlePlayerTimeupdate"
      @player-play="handlePlayerPlay"
      @player-pause="handlePlayerPause"
      @player-ended="handlePlayerEnded"
      @player-start-seek="handlePlayerStartSeek"
      @player-complete-seek="handlePlayerCompleteSeek"
    >
      <template #default>
        <InVideoQuizModal
          :visible.sync="isInVideoQuizModalVisible"
          :question="inVideoQuizQuestion"
          @submit="handleInVideoQuizSubmit"
        />

        <ActivityCheckModal
          :visible.sync="isActivityCheckModalVisible"
          @continue="handleActivityCheckContinue"
        />
      </template>
    </AliyunPlayer>

    <div v-if="showDebugInfo && isPlayerVisible" class="player-state">
      <p>playing: {{ isVideoPlaying }}</p>

      <p>isForcedPaused: {{ isForcedPaused }}</p>

      <p>currentTime: {{ currentTime }}</p>

      <p>duration: {{ videoDuration }}</p>

      <p>syncIntervalTime: {{ syncIntervalTime }}</p>

      <p>maxPlaybackTime: {{ videoMaxPlaybackTime }}</p>

      <p>视频内测验弹窗可见: {{ isInVideoQuizModalVisible }}</p>

      <p>活跃度检测弹窗可见: {{ isActivityCheckModalVisible }}</p>
    </div>
  </div>
</template>

<script>
import { saveCoursePosition, getCoursePosition } from '@/api/training-examination/my-training'
import { getTrainingVideoDetail } from '@/api/training-examination/training-course'
import { getFileDetail } from '@/api/common'
import { getPlayInfo } from '@/api/aliyun-vod'
import AliyunPlayer from '@/components/aliyun-player'
import FilePreview from './file-preview'
import InVideoQuizModal from './in-video-quiz-modal'
import ActivityCheckModal from './activity-check-modal'
import { CONSTANTS } from '@/constants/course'
import debounce from 'lodash.debounce'

export default {
  name: 'TrainingCourse',
  components: { AliyunPlayer, FilePreview, InVideoQuizModal, ActivityCheckModal },
  props: {
    trainingId: {
      type: String,
      default: ''
    },

    courseId: {
      type: String,
      default: ''
    },

    // 视频内测验，答错题目后，后退的时间，单位秒
    rewindTimeOnError: {
      type: Number,
      default: 3 * 60 // 3分钟
    },

    // 活跃度检测
    enableActivityDetection: {
      type: Boolean,
      default: true
    }

  },

  data() {
    return {
      CONSTANTS,
      // 强制暂停时，隐藏播放器的播放控制控件
      showDebugInfo: false,
      isForcedPaused: false,
      isCourseLoading: false,
      isPlaybackUrlLoading: false,
      isFileUrlLoading: false,
      // 课程进度加载状态
      isLastPositionLoading: false,
      // 是否开始播放了
      isPlaybackStarted: false,
      // 是否正在播放
      isVideoPlaying: false,
      isActivityCheckModalVisible: false,

      inVideoQuizQuestion: null,
      seekStartTime: 0,
      seekCompleteTime: 0,

      // 每播放1分钟，同步播放进度
      syncIntervalTime: 60,
      maxPlaybackTime: 0,
      playerConfig: {
        height: '600px'
      },

      course: {},
      videoInfo: {},
      videoPlaybackUrl: '',
      videoMaxPlaybackTime: 0,
      videoDuration: Infinity,
      currentTime: 0,
      // 上次播放位置
      lastPosition: '',

      filePreviewUrl: '',
      fileInfo: {},
      // 检测间隔，单位秒，启用活跃度检测时有效
      detectionInterval: 3 * 60,
      defaultDetectionInterval: 3 * 60
    }
  },

  computed: {
    isInVideoQuizModalVisible() {
      return this.inVideoQuizQuestion && this.isPlaybackStarted && this.isForcedPaused
    },

    isFilePreviewVisible() {
      return this.course.resourceType === CONSTANTS.COURSE_TYPE_TEXT_IMG
        && this.course.videoPath
    },

    isPlayerVisible() {
      return this.course.resourceType === CONSTANTS.COURSE_TYPE_VIDEO
        && this.course.videoPath
        && this.videoPlaybackUrl
        && !this.isLastPositionLoading
    },

    questionList() {
      const list = this.course.questionDTOList
      if (this.course && list) {
        return [...list].sort((a, b) => a.second - b.second)
      } else {
        return []
      }
    }
  },

  watch: {
    courseId: {
      handler(id) {
        if (!id) return
        this.resetState()
        this.getCourseDetail(id)
      },

      immediate: true
    },

    // 视频播放时，定时检测活跃情况，视频暂停时，停止活跃检测
    isVideoPlaying(val) {
      // 没有测验问题才进行活跃度检测
      // 如果在resetState里面重置了course对象，则需要留意，stopActivityCheck不会执行
      if (!this.questionList.length) {
        if (val) {
          this.startActivityCheck()
        } else {
          this.stopActivityCheck()
        }
      }
    }
  },

  created() {
    this.setupAutoPause()
    // TODO: 删除调试相关代码
    this.showDebugInfo = !!localStorage.getItem('training-course-debug')
    this.debouncedUpdateCoursePosition = debounce(this.updateCoursePosition, 1000)
  },

  beforeDestroy() {
    this.cleanupAutoPause()
    this.stopActivityCheck()
  },

  methods: {
    handleVisibilityChange(e) {
      if (document.hidden) {
        if (this.isPlaybackStarted) {
          this.$refs.playerRef.pause()
        }
      }
    },

    handleBlur() {
      if (this.isPlaybackStarted) {
        this.$refs.playerRef.pause()
      }
    },

    setupAutoPause() {
      document.addEventListener('visibilitychange', this.handleVisibilityChange)
      window.addEventListener('blur', this.handleBlur)
    },

    cleanupAutoPause() {
      document.removeEventListener('visibilitychange', this.handleVisibilityChange)
      window.removeEventListener('blur', this.handleBlur)
    },

    // 注意：此处没有重置course对象
    resetState() {
      if (this.$refs.playerRef) {
        this.$refs.playerRef.closeMessage()
        this.$refs.playerRef.pause()
      }

      this.isForcedPaused = false
      this.isPlaybackStarted = false
      this.inVideoQuizQuestion = null
      this.videoDuration = Infinity
      this.detectionInterval = this.defaultDetectionInterval
      this.seekStartTime = 0
      this.seekCompleteTime = 0
      this.videoPlaybackUrl = ''
      this.videoMaxPlaybackTime = 0
      this.currentTime = 0
      this.filePreviewUrl = ''
      this.lastPosition = 0
    },

    // getCourseProgress() {
    //   if (this.course.resourceType === CONSTANTS.COURSE_TYPE_TEXT_IMG) {
    //     return Promise.resolve()
    //   } else {
    //     return this.getMaxPlaybackTime()
    //   }
    // },

    // 获取上次课程进度
    getLastPosition() {
      const requestId = this.courseId
      this.isLastPositionLoading = true
      return getCoursePosition({
        userId: this.$store.getters.userId,
        videoId: this.courseId,
        trainId: this.trainingId
      })
        .then((res) => {
          if (requestId !== this.courseId) return
          console.log('课程进度', res.data)
          if (!res.data) {
            this.lastPosition = 0
            return
          }

          if (this.course.resourceType === CONSTANTS.COURSE_TYPE_TEXT_IMG) {
            this.lastPosition = 0
            // 图文课程暂不实现进度恢复功能
            return
          } else {
            this.lastPosition = res.data.currentPosition
            console.log('最大播放进度', res)
          }
        })
        .finally(() => {
          this.isLastPositionLoading = false
        })
    },

    checkActivity() {
      this.isForcedPaused = true
      this.$refs.playerRef.pause()
      this.isActivityCheckModalVisible = true
    },

    startActivityCheck() {
      let delay = this.defaultDetectionInterval
      if (this.videoDuration < this.defaultDetectionInterval) {
        delay = this.videoDuration / 2
      }

      // 防止下次检测的时间，超过视频剩余播放时间
      if (this.currentTime + delay >= this.videoDuration) {
        return
      }

      this.activityCheckTimer = setTimeout(() => {
        this.checkActivity()
      }, delay * 1000)
    },

    stopActivityCheck() {
      if (!this.activityCheckTimer) return
      clearTimeout(this.activityCheckTimer)
      this.activityCheckTimer = null
    },

    handleActivityCheckContinue(resume = false) {
      if (resume) {
        this.$refs.playerRef.play()
      }
      this.startActivityCheck()
    },

    // 获取当前课程进度
    getCurrentPosition() {
      // 图文进度默认100%
      if (this.course.resourceType === CONSTANTS.COURSE_TYPE_TEXT_IMG) {
        return {
          currentPosition: 100,
          videoTotal: 100,
          percent: 100
        }
      } else {
        return {
          currentPosition: this.currentTime,
          videoTotal: this.videoDuration,
          percent: parseInt(this.currentTime / this.videoDuration * 100)
        }
      }
    },

    updateCoursePosition(position) {
      const payload = {
        userId: this.$store.getters.userId,
        videoId: this.courseId,
        trainId: this.trainingId,
        ...this.getCurrentPosition()
      }

      if (typeof position === 'object') {
        Object.assign(payload, position)
      }

      return saveCoursePosition(payload)
        .then((res) => {
          console.log('同步响应', res)
        })
    },

    getFileUrl(id) {
      this.isFileUrlLoading = true
      const requestId = id
      return getFileDetail(id)
        .then((res) => {
          if (requestId !== this.course.videoPath) return
          this.fileInfo = res.data
          this.filePreviewUrl = res.data.fileLink
        })
        .catch((err) => {
          console.log('获取文件详情失败')
          console.log(err)
        })
        .finally(() => {
          if (requestId !== this.course.videoPath) return
          this.isFileUrlLoading = false
        })
    },

    getVideoPlaybackUrl(id) {
      const requestId = id
      this.isPlaybackUrlLoading = true
      return getPlayInfo(id)
        .then((res) => {
          if (requestId !== this.course.videoPath) return
          console.log('视频播放信息', res)
          this.videoInfo = res.data.body
          const playInfo = this.videoInfo.playInfoList.playInfo[0]
          this.videoPlaybackUrl = playInfo.playURL
          this.videoDuration = parseFloat(playInfo.duration)
        })
        .catch((err) => {
          console.log(err)
          this.$message.error('获取视频播放信息失败')
        })
        .finally(() => {
          this.isPlaybackUrlLoading = false
        })
    },

    getCourseFileUrl(course) {
      if (course.resourceType === CONSTANTS.COURSE_TYPE_TEXT_IMG) {
        return this.getFileUrl(course.videoPath)
      } else {
        return this.getVideoPlaybackUrl(course.videoPath)
      }
    },

    adaptCourse(course) {
      if (!Array.isArray(course.questionDTOList)) {
        course.questionDTOList = []
      }

      course.questionDTOList.forEach((question) => {
        // question.passed = false
        question.second = parseFloat(question.second)
      })
      return course
    },

    getCourseDetail(id) {
      const requestId = id
      this.isCourseLoading = true
      getTrainingVideoDetail({ id })
        .then((res) => {
          console.log('课程详情', res.data, res.data.videoPath)
          if (this.courseId !== requestId) return
          this.course = this.adaptCourse(res.data)
          return this.getCourseFileUrl(res.data)
        })
        .then(() => this.getLastPosition())
        .catch((err) => {
          this.$message.error('获取课程信息失败')
          console.log(err)
        })
        .finally(() => {
          if (this.courseId !== requestId) return
          this.isCourseLoading = false
        })
    },

    handlePlayerReady({ player, args }) {
      if (this.enableActivityDetection) {
        player.setProgressMarkers(this.questionList.map((question) => ({
          offset: Math.floor(question.second),
          text: question.content
        })))
      }

      if (this.lastPosition) {
        // this.$refs.playerRef.showMessage('已恢复到上次观看进度', {
        //   duration: 3000,
        //   position: ['x-start', 'y-end']
        // })
        player.seek(this.lastPosition)
      }
    },

    getQuestionByTime(time) {
      const questionIndex = this.questionList.findIndex((question) => {
        return Math.floor(question.second) === Math.floor(time)
      })

      if (questionIndex > -1) {
        return this.questionList[questionIndex]
      } else {
        return null
      }
    },

    handlePlayerTimeupdate({ player, args }) {
      this.currentTime = player.getCurrentTime()

      // 用户手动回退时，不进行活跃度检测
      if (this.currentTime < this.videoMaxPlaybackTime || !this.enableActivityDetection) {
        return
      }

      const question = this.getQuestionByTime(this.currentTime)

      this.inVideoQuizQuestion = question
      // 当前时间节点存在测验问题，强制暂停

      // if (this.isPlaybackStarted && question && !question.passed) {
      if (this.isPlaybackStarted && question && !question.passed) {
        this.$refs.playerRef.pause()
        this.isForcedPaused = true
      } else {
        this.isForcedPaused = false
      }

      // 当用户手动回退时，不同步播放进度，不检测活跃度
      const needSync = parseInt(this.currentTime) % this.syncIntervalTime === 0
        && this.currentTime >= this.videoMaxPlaybackTime
        && this.currentTime >= 1
      // 防止0s时同步进度

      console.log({
        currentTime: parseInt(this.currentTime),
        mod: parseInt(this.currentTime) % this.syncIntervalTime,
        videoMaxPlaybackTime: this.videoMaxPlaybackTime,
        needSync
      })

      if (needSync) {
        console.log('开始同步播放进度')
        this.debouncedUpdateCoursePosition()
      }
    },

    handlePlayerRequestFullScreen({ player, args }) {
      console.log('player enter full screen')
    },

    handlePlayerCancelFullScreen({ player, args }) {
      console.log('player exit full screen')
    },

    handleInVideoQuizSubmit(isCorrect) {
      // this.inVideoQuizQuestion.passed = isCorrect
      this.$refs.playerRef.play()
      this.isForcedPaused = false

      if (!isCorrect) {
        this.$refs.playerRef.showMessage('回答错误，请重新学习')
        this.seekToLastCheckpoint()
      }
    },

    seekToLastCheckpoint() {
      // questionList已按时间递增排序
      // 反转questionList,按时间递减，查询当前问题的上一个问题
      const lastQuestion = [...this.questionList].reverse()
        .find((question) => question.second < this.inVideoQuizQuestion.second)
      let time

      // 答错后，后退的目标时间和上一个问题出现的时间之间取最大值，即时间轴上最近的时间
      // 也就是说，假如在后退的时间段内存在问题，则将跳到问题所在到时间
      if (lastQuestion) {
        time = Math.max(lastQuestion.second, Math.floor(this.currentTime - this.rewindTimeOnError))
      } else {
        // 防止回退到负数
        time = Math.max(0, this.currentTime - this.rewindTimeOnError)
      }

      this.$refs.playerRef.seek(time)
    },

    handlePlayerPlay() {
      console.log('播放了')
      this.isVideoPlaying = true
      this.isPlaybackStarted = true
    },

    handlePlayerPause() {
      console.log('暂停了')
      this.isVideoPlaying = false
    },

    handlePlayerEnded() {
      this.isVideoPlaying = false
      this.isPlaybackStarted = false
      console.log('ended', this.currentTime, this.videoDuration)
      // 播放结束时currentTime略微小于视频实际时长，所以需要用实际时长更新进度
      this.updateCoursePosition({
        percent: 100,
        currentPosition: this.videoDuration,
        videoTotal: this.videoDuration
      })
    },

    handlePlayerCompleteSeek({ player, args }) {
      this.seekCompleteTime = args[0].paramData
      const isRewind = this.seekStartTime > this.seekCompleteTime
      console.log(isRewind ? '回退' : '快进/原地')

      // 用户手动回退(触发completeSeek和startSeek事件)需要重新回答测验问题
      // 答错问题后，组件回退不需要重新回答已经答对的测验问题
      if (isRewind) {
        // 当回退到测验问题之后的时间节点时（精确到秒），才重置问题状态
        this.questionList.filter((question) => parseFloat(question.second) > this.seekCompleteTime)
          .forEach((question) => {
            // question.passed = false
          })
      }
    },

    handlePlayerStartSeek({ player }) {
      this.seekStartTime = player.getCurrentTime()
      console.log('seekStart', this.seekStartTime)
    },

    handleIframeLoaded() {
      this.updateCoursePosition()
    }
  }
}
</script>

<style lang="scss" scoped>
.training-course {
  &.player-paused {
    ::v-deep :is(
      .prism-play-btn,
      .prism-big-play-btn,
      .prism-progress-cursor,
      .prism-play-btn
    ) {
      display: none !important;
    }

    ::v-deep .prism-progress {
      pointer-events: none;
    }
  }
}
</style>
