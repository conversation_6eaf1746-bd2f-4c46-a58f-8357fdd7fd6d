<template>
  <div
    v-loading="isCourseLoading"
    class="training-course"
    :class="isForcedPaused ? 'player-paused' : ''"
  >
    <FilePreview
      v-if="isFilePreviewVisible"
      ref="filePreviewRef"
      :source="filePreviewUrl"
      height="100%"
    />

    <AliyunPlayer
      v-if="isPlayerVisible"
      ref="playerRef"
      :source="videoPlaybackUrl"
      :max-playback-time.sync="videoMaxPlaybackTime"
      :config="playerConfig"
      @player-ready="handlePlayerReady"
      @player-timeupdate="handlePlayerTimeupdate"
      @player-play="handlePlayerPlay"
      @player-pause="handlePlayerPause"
      @player-ended="handlePlayerEnded"
      @player-start-seek="handlePlayerStartSeek"
      @player-complete-seek="handlePlayerCompleteSeek"
    >
      <template #default>
        <transition name="player-modal">
          <InVideoQuiz
            v-if="isInVideoQuizVisible"
            ref="quizRef"
            :question="inVideoQuizQuestion"
            @submit="handleInVideoQuizSubmit"
          />
        </transition>
      </template>
    </AliyunPlayer>
  </div>
</template>

<script>
import { CONSTANTS } from '@/constants/course'
import { getTrainingVideoDetail } from '@/api/training-examination/training-course'
import { getFileDetail } from '@/api/common'
import { getPlayInfo } from '@/api/aliyun-vod'
import AliyunPlayer from '@/components/aliyun-player'
import FilePreview from './file-preview'
import InVideoQuiz from './in-video-quiz'

export default {
  name: 'VideoCourse',
  components: { AliyunPlayer, FilePreview, InVideoQuiz },
  props: {
    courseId: {
      type: String,
      require: true,
      default: ''
    },

    // 视频内测验，课程资源类型为视频时且配套题目时有效
    enableInVideoQuiz: {
      type: Boolean,
      default: true
    },

    // 视频内测验，答错题目后，后退的时间，单位秒
    rewindTimeOnError: {
      type: Number,
      default: 3 * 60 // 3分钟
    },

    // 活跃度检测
    enableActivityDetection: {
      type: Boolean,
      default: true
    },

    // 检测间隔，单位秒，启用活跃度检测时有效
    detectionInterval: {
      type: Number,
      default: 3 * 60 // 3分钟
    }
  },

  data() {
    return {
      CONSTANTS,
      isForcedPaused: false,
      isCourseLoading: false,
      isPlaybackUrlLoading: false,
      isFileUrlLoading: false,
      isPlaybackStarted: false,

      inVideoQuizQuestion: null,
      seekStartTime: 0,
      seekCompleteTime: 0,

      maxPlaybackTime: 0,
      playerConfig: {
        height: '600px'
      },

      course: {},
      videoInfo: {},
      videoPlaybackUrl: '',
      videoMaxPlaybackTime: 0,
      currentTime: -1,

      filePreviewUrl: '',
      fileInfo: {}
    }
  },

  computed: {
    isInVideoQuizVisible() {
      return this.inVideoQuizQuestion && this.isPlaybackStarted && this.isForcedPaused
    },

    isFilePreviewVisible() {
      return this.course.resourceType === CONSTANTS.COURSE_TYPE_TEXT_IMG
        && this.course.videoPath
    },

    isPlayerVisible() {
      return this.course.resourceType === CONSTANTS.COURSE_TYPE_VIDEO
        && this.course.videoPath
        && this.videoPlaybackUrl
    },

    questionList() {
      const list = this.course.questionDTOList
      if (this.course && list) {
        return [...list].sort((a, b) => a.second - b.second)
      } else {
        return {}
      }
    }
  },

  watch: {
    courseId: {
      handler(id) {
        if (!id) return
        this.getCourseDetail(id)
      },

      immediate: true
    }
  },

  methods: {
    getFileUrl(id) {
      this.isFileUrlLoading = true
      const requestId = id
      getFileDetail(id)
        .then((res) => {
          if (requestId !== this.course.videoPath) return
          this.fileInfo = res.data
          this.filePreviewUrl = res.data.fileLink
        })
        .finally(() => {
          if (requestId !== this.course.videoPath) return
          this.isFileUrlLoading = false
        })
    },

    getVideoPlaybackUrl(id) {
      console.log('获取视频播放信息', id)
      const requestId = id
      this.isPlaybackUrlLoading = true
      return getPlayInfo(id)
        .then((res) => {
          if (requestId !== this.course.videoPath) return
          console.log('视频播放信息', res)
          this.videoInfo = res.data.body
          this.videoPlaybackUrl = this.videoInfo.playInfoList.playInfo[0].playURL
        })
        .catch((err) => {
          console.log(err)
          this.$message.error('获取视频播放信息失败')
        })
        .finally(() => {
          this.isPlaybackUrlLoading = false
        })
    },

    getCourseFileUrl(course) {
      if (course.resourceType === '1') {
        return this.getFileUrl(course.videoPath)
      } else {
        return this.getVideoPlaybackUrl(course.videoPath)
      }
    },

    adaptCourse(course) {
      course.questionDTOList.forEach((question) => {
        question.passed = false
        question.second = parseFloat(question.second)
      })
      return course
    },

    getCourseDetail(id) {
      const requestId = id
      this.isCourseLoading = true
      this.isPlaybackStarted = false
      getTrainingVideoDetail({ id })
        .then((res) => {
          console.log('课程详情', res.data, res.data.videoPath)
          if (this.courseId !== requestId) return
          this.course = this.adaptCourse(res.data)
          return this.getCourseFileUrl(res.data)
        })
        .catch((err) => {
          this.$message.error('获取课程信息失败')
          console.log(err)
        })
        .finally(() => {
          if (this.courseId !== requestId) return
          this.isCourseLoading = false
        })
    },

    handlePlayerReady({ player, args }) {
      if (this.enableInVideoQuiz) {
        player.setProgressMarkers(this.questionList.map((question) => ({
          offset: Math.floor(question.second),
          text: question.content
        })))
      }
    },

    getQuestionByTime(time) {
      const questionIndex = this.questionList.findIndex((question) => {
        return Math.floor(question.second) === Math.floor(time)
      })

      if (questionIndex > -1) {
        return this.questionList[questionIndex]
      } else {
        return null
      }
    },

    handlePlayerTimeupdate({ player, args }) {
      this.currentTime = player.getCurrentTime()

      if (this.enableInVideoQuiz) {
        const question = this.getQuestionByTime(this.currentTime)

        this.inVideoQuizQuestion = question
        // 当前时间节点存在测验问题，强制暂停

        if (this.isPlaybackStarted && question && !question.passed) {
          this.$refs.playerRef.pause()
          this.isForcedPaused = true
        } else {
          this.isForcedPaused = false
        }
      }
    },

    handlePlayerRequestFullScreen({ player, args }) {
      console.log('player enter full screen')
    },

    handlePlayerCancelFullScreen({ player, args }) {
      console.log('player exit full screen')
    },

    handleInVideoQuizSubmit(isCorrect) {
      this.inVideoQuizQuestion.passed = isCorrect
      this.$refs.playerRef.play()
      this.isForcedPaused = false

      if (!isCorrect) {
        this.$refs.playerRef.showMessage('回答错误，请重新学习')
        this.seekToLastCheckpoint()
      }
    },

    seekToLastCheckpoint() {
      // questionList已按时间递增排序
      // 反转questionList,按时间递减，查询当前问题的上一个问题
      const lastQuestion = [...this.questionList].reverse()
        .find((question) => question.second < this.inVideoQuizQuestion.second)
      let time

      // 答错后，后退的目标时间和上一个问题出现的时间之间取最大值，即时间轴上最近的时间
      // 也就是说，假如在后退的时间段内存在问题，则将跳到问题所在到时间
      if (lastQuestion) {
        time = Math.max(lastQuestion.second, Math.floor(this.currentTime - this.rewindTimeOnError))
      } else {
        // 防止回退到负数
        time = Math.max(0, this.currentTime - this.rewindTimeOnError)
      }

      this.$refs.playerRef.seek(time)
    },

    handlePlayerPlay() {
      this.isUserWatching = true
      this.isPlaybackStarted = true
    },

    handlePlayerPause() {
      this.isUserWatching = false
    },

    handlePlayerEnded() {
      this.isUserWatching = false
      this.isPlaybackStarted = false
      console.log('ended')
    },

    handlePlayerCompleteSeek({ player, args }) {
      this.seekCompleteTime = args[0].paramData
      const isRewind = this.seekStartTime > this.seekCompleteTime
      console.log(isRewind ? '回退' : '快进/原地')

      // 用户手动回退(触发completeSeek和startSeek事件)需要重新回答测验问题
      // 答错问题后，组件回退不需要重新回答已经答对的测验问题
      if (isRewind) {
        // 当回退到测验问题之后的时间节点时（精确到秒），才重置问题状态
        this.questionList.filter((question) => parseFloat(question.second) > this.seekCompleteTime)
          .forEach((question) => {
            question.passed = false
          })
      }
    },

    handlePlayerStartSeek({ player }) {
      this.seekStartTime = player.getCurrentTime()
      console.log('seekStart', this.seekStartTime)
    }

  }
}
</script>

<style lang="scss" scoped>
.training-course {
  &.player-paused {
    ::v-deep :is(
      .prism-play-btn,
      .prism-big-play-btn,
      .prism-progress-cursor,
      .prism-play-btn
    ) {
      display: none !important;
    }

    ::v-deep .prism-progress {
      pointer-events: none;
    }
  }

  .player-modal-enter-active,
  .player-modal-leave-active {
    transition: all 500ms ease-in-out;
  }

  .player-modal-enter,
  .player-modal-leave-to {
    background-color: #00000000;
    backdrop-filter: blur(0px);

    ::v-deep .in-video-quiz {
      transform: scale(0.5);
      filter: opacity(0);
    }
  }
}
</style>
