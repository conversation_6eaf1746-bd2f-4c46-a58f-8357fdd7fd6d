<template>
  <el-dialog
    title="选择试题"
    width="60%"
    append-to-body
    class="question-picker"
    top="10vh"
    :visible.sync="visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @open="handleOpen"
    @closed="handleClosed"
  >
    <div class="picker-container">
      <div v-loading="classificationsLoading" class="picker-sidebar">
        <el-input v-model="classificationName" class="picker-search-name" @keydown.enter="getClassifications">
          <template #suffix>
            <i class="el-icon-search picker-search-icon" @click="getClassifications" />
          </template>
        </el-input>

        <ul class="picker-classification-list">
          <li
            v-for="classification in classifications"
            :key="classification.id"
            :class="[
              'picker-classification-item',
              classification.id === queryParams.classification ? 'picker-classification-item--current' : ''
            ]"
            @click="handleClassificationClick(classification)"
          >
            {{ classification.classificationName }}
          </li>
        </ul>
      </div>

      <div v-loading="questionsLoading || loading" class="picker-content">
        <el-select
          v-model="queryParams.quesType"
          class="picker-search-type"
          clearable
          @change="getQuestions"
          @clear="getQuestions"
        >
          <el-option
            v-for="type in questionTypeOptions"
            :key="type.value"
            :label="type.label"
            :value="type.value"
          />
        </el-select>

        <el-table
          ref="tableRef"
          class="picker-table"
          :data="questions"
          border
          height="100%"
          highlight-current-row
          @select="handleSelect"
          @row-click="handleRowClick"
        >
          <el-table-column type="selection" align="center" />

          <el-table-column label="试题" prop="content" align="center" />

          <el-table-column
            label="题型"
            prop="quesType"
            :formatter="questionTypeFormatter"
            align="center"
            width="90"
          />
        </el-table>

        <dt-pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNo"
          :limit.sync="queryParams.pageSize"
          @pagination="getQuestions"
        />
      </div>
    </div>

    <div slot="footer">
      <el-button type="primary" @click="handleConfirm">
        确定
      </el-button>

      <el-button @click="handleCancel">
        取消
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getQuestionClassification, getQuestionsByClassification } from '@/api/learning-manage'
import { questionTypeFormatter } from '@/utils/question'
import { QUESTION_TYPE_OPTIONS } from '@/constants/question'

export default {
  name: 'QuestionPickerDialog',
  data() {
    return {
      visible: false,
      total: 0,
      classificationName: '',
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        status: 1,
        classification: '',
        quesType: ''
      },


      classifications: [],
      questions: [],
      questionTypeOptions: QUESTION_TYPE_OPTIONS,

      pickedQuestion: null,

      loading: false,
      classificationsLoading: false,
      questionsLoading: false
    }
  },

  created() {
  },

  mounted() {
  },

  methods: {
    pick() {
      this.visible = true
      return new Promise((resolve, reject) => {
        this.pickerResolve = resolve
        this.pickerReject = reject
      })
    },

    handleOpen() {
      this.getPickerData()
    },

    async getPickerData() {
      this.loading = true
      try {
        await this.getClassifications()
        if (this.classifications.length) {
          this.queryParams.classification = this.classifications[0].id
        }
        await this.getQuestions()
      } catch (err) {
        console.log('题库获取失败')
        console.error(err)
      } finally {
        this.loading = false
      }
    },

    resetPickerData() {
      this.questions = []
      this.classifications = []
      this.classificationName = ''
      this.pickedQuestion = null
      this.pickerResolve = null
      this.pickerReject = null
      this.questionsLoading = false
      this.classificationsLoading = false
      this.total = 0
      this.queryParams = {
        pageNo: 1,
        pageSize: 10,
        classification: '',
        quesType: '',
        status: 1
      }
    },

    handleSelect(selection) {
      this.$refs.tableRef.clearSelection()
      if (selection.length) {
        const row = selection[selection.length - 1]
        this.$refs.tableRef.toggleRowSelection(row, true)
        this.$refs.tableRef.setCurrentRow(row)
        this.pickedQuestion = { ...row }
      } else {
        this.$refs.tableRef.setCurrentRow()
        this.pickedQuestion = null
      }
    },

    handleRowClick(row) {
      this.$refs.tableRef.clearSelection()
      this.$refs.tableRef.toggleRowSelection(row, true)
      this.$refs.tableRef.setCurrentRow(row)
      this.pickedQuestion = { ...row }
    },

    getClassifications() {
      this.classificationsLoading = true
      const classificationName = this.classificationName
      return getQuestionClassification({
        status: 1,
        classificationName
      })
        .then((res) => {
          if (this.classificationName !== classificationName) return
          this.classifications = res.data
        })
        .finally(() => {
          if (this.classificationName !== classificationName) return
          this.classificationsLoading = false
        })
    },

    getQuestions() {
      this.questionsLoading = true
      const key = JSON.stringify(this.queryParams)
      return getQuestionsByClassification(this.queryParams)
        .then((res) => {
          const currentKey = JSON.stringify(this.queryParams)
          if (currentKey !== key) return
          this.questions = res.data.rows
          this.total = res.data.totalRows
        })
        .finally(() => {
          const currentKey = JSON.stringify(this.queryParams)
          if (currentKey !== key) return
          this.questionsLoading = false
        })
    },

    handleClassificationClick(classification) {
      this.queryParams.classification = classification.id
      this.queryParams.pageNo = 1
      this.getQuestions()
    },

    handleConfirm() {
      if (!this.pickedQuestion) {
        this.$message.warning('请选择试题')
        return
      }

      this.visible = false
    },

    handleCancel() {
      this.pickedQuestion = null
      this.visible = false
    },

    handleClosed() {
      if (this.pickedQuestion) {
        this.pickerResolve(this.pickedQuestion)
      } else {
        this.pickerReject()
      }
      this.resetPickerData()
    },

    questionTypeFormatter
  }
}
</script>

<style lang="scss" scoped>
.question-picker {
  --margin-top: 10vh;
  --header-height: 72px;
  --footer-height: 84px;
  --margin-bottom: 10vh;


  .picker-container {
    display: flex;
    flex-flow: row nowrap;
    gap: 10px;
    height: calc(100vh - var(--margin-top) - var(--footer-height) - var(--header-height) - var(--margin-bottom));
  }

  .picker-sidebar {
    --border-color: #d9dde3;
    overflow: auto;
    flex: 0 0 260px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 15px;
  }

  .picker-classification-list {
    list-style: none;
    margin: 10px 0 0;
    padding: 0;
    height: calc(100% - 10px - 36px);
    overflow: auto;
    border: 1px solid var(--border-color);
    border-radius: 4px;
  }

  .picker-classification-item {
    padding: 10px;
    text-align: center;
    cursor: pointer;

    &:not(:last-child) {
      border-bottom: 1px solid var(--border-color);
    }

    &:hover {
      background-color: #F5F7FA;
    }

    &--current {
      background-color: var(--primary-background-hover, rgba(11, 204, 39, 0.1));
    }
  }

  .picker-content {
    display: flex;
    min-width: 0;
    flex-flow: column nowrap;
    flex: 1 1 auto;
    gap: 10px;
  }

  .picker-search-type {
    align-self: flex-end;
  }

  .picker-search-icon {
    cursor: pointer;
    font-size: 16px;
    line-height: 36px;
    margin-right: 5px;
    color: var(--primary);
  }

  ::v-deep .picker-table {
    .el-table__header {
      .el-table__cell {
        background-color: rgb(242, 242, 242);
      }

      .el-table-column--selection .el-checkbox {
        display: none;
      }
    }

    .el-table__body {
      .el-table__row {
        cursor: pointer
      }
    }
  }
}
</style>
