<template>
  <transition
    name="player-modal"
    @after-enter="handleAfterEnter"
    @after-leave="handleAfterLeave"
  >
    <div
      v-if="visible"
      class="player-modal-mask"
    >
      <el-card :header="title" class="player-modal-content">
        <slot />
      </el-card>
    </div>
  </transition>
</template>

<script>
export default {
  name: 'PlayerModal',

  props: {
    visible: {
      type: Boolean,
      default: false
    },

    title: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
    }
  },

  watch: {
  },

  mounted() {},

  methods: {
    handleAfterEnter() {
      this.$emit('opened')
    },

    handleAfterLeave() {
      this.$emit('closed')
    }
  }
}
</script>

<style lang="scss" scoped>
.player-modal-mask {
  --player-controlbar-height: 40px;
  --inset-bottom: calc(var(--player-controlbar-height));

  position: absolute;
  inset: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.player-modal-content {
  background-color: #fff;
  width: 80%;
  max-width: 700px;

  ::v-deep .el-card__header {
    font-weight: bold;
    font-size: 16px;
  }
}

.player-modal-enter-active,
.player-modal-leave-active {
  transition: all 300ms ease-in-out;

  .player-modal-content {
    transition: all 300ms ease-in-out;
  }
}

.player-modal-enter-to,
.player-modal-leave {
  background-color: #00000085;
  backdrop-filter: blur(20px);

  .player-modal-content {
    transform: scale(1);
    opacity: 1;
  }
}

.player-modal-enter,
.player-modal-leave-to {
  background-color: #00000000;
  backdrop-filter: blur(0px);

  .player-modal-content {
    transform: scale(0.5);
    opacity: 0;
  }
}
</style>

