<template>
  <tranisition name="player-modal">
    <div
      v-show="visible"
      class="player-modal-mask"
    >
      <el-card :header="title" class="player-modal-content">
        <slot />
      </el-card>
    </div>
  </tranisition>
</template>

<script>
export default {
  name: 'PlayerModal',

  props: {
    visible: {
      type: Boolean,
      default: false
    },

    title: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
    }
  },

  mounted() {},

  methods: {
  }
}
</script>

<style lang="scss" scoped>
.player-modal-mask {
  --player-controlbar-height: 40px;
  --inset-bottom: calc(var(--player-controlbar-height));

  background-color: #00000085;
  backdrop-filter: blur(20px);
  position: absolute;
  inset: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.player-modal-content {
  background-color: #fff;
  width: 80%;
  max-width: 700px;

  ::v-deep .el-card__header {
    font-weight: bold;
    font-size: 16px;
  }
}

.player-modal-enter-active,
.player-modal-leave-active {
  transition: all 500ms ease-in-out;
}

.player-modal-enter,
.player-modal-leave-to {
  background-color: #00000000;
  backdrop-filter: blur(0px);

  ::v-deep .player-modal-content {
    transform: scale(0.5);
    filter: opacity(0);
  }
}
</style>

