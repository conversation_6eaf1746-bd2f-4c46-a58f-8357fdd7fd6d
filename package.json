{"name": "vue-admin", "version": "4.4.0", "description": "A vue admin template with Element UI & axios & iconfont & permission control & lint", "author": "hbdt", "scripts": {"dev": "cross-env NODE_OPTIONS=--openssl-legacy-provider vue-cli-service serve --mode=development ", "build:test": "cross-env NODE_OPTIONS=--openssl-legacy-provider  vue-cli-service build --mode=test", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "lint:report": "eslint -f json -o report.json --fix --ext .js,.vue src || exit 0", "lint": "eslint --fix --ext .js,.vue src", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit", "postinstall": "patch-package", "mock": "cross-env VUE_APP_MOCK=true vue-cli-service serve --mode=development", "prepare": "husky install", "stylelint": "stylelint --fix src/**/*.{css,less,vue,scss}"}, "dependencies": {"@jiaminghi/data-view": "^2.10.0", "@tinymce/tinymce-vue": "^3.2.8", "@vue-office/docx": "^1.6.3", "@vue-office/excel": "^1.7.14", "@vue-office/pdf": "^2.0.10", "@vue/composition-api": "^1.7.2", "DQBasic-vue-component": "2.3.128", "aliyun-aliplayer": "^2.30.3", "axios": "0.18.1", "babel-plugin-transform-require-context": "^0.1.1", "clipboard": "^2.0.11", "core-js": "^3.22.2", "dayjs": "^1.11.13", "decimal.js": "^10.4.3", "docx-preview": "^0.1.8", "e-icon-picker": "^1.1.6", "echarts": "^4.9.0", "el-table-virtual-scroll": "^1.0.32", "element-ui": "^2.15.14", "exceljs": "^4.4.0", "file-saver": "2.0.1", "form-gen-parser": "^1.0.3", "fuse.js": "^6.6.2", "gm-crypt": "^0.0.2", "html2canvas": "^1.4.1", "js-base64": "^3.7.2", "js-cookie": "2.2.0", "jsencrypt": "^3.2.1", "jszip": "^3.7.1", "lodash": "^4.17.21", "moment": "^2.30.1", "normalize.css": "7.0.0", "nprogress": "0.2.0", "path-to-regexp": "2.4.0", "pdfjs-dist": "2.5.207", "pdfvuer": "1.9.2", "pl-table": "^2.7.5", "postcss": "^8.4.33", "postcss-pxtorem": "^5.1.1", "qs": "^6.10.3", "resize-observer-polyfill": "^1.5.1", "screenfull": "4.2.0", "sortablejs": "^1.15.1", "theme-vue": "0.0.8", "tinymce": "^5.10.3", "vcrontab": "^0.3.5", "vue": "2.6.10", "vue-awesome-swiper": "^3.1.3", "vue-count-to": "^1.0.13", "vue-cropper": "^0.5.8", "vue-demi": "^0.14.6", "vue-esign": "^1.1.4", "vue-grid-layout": "^2.4.0", "vue-i18n": "7.3.2", "vue-pdf": "^4.3.0", "vue-print-nb": "^1.7.5", "vue-router": "^3.5.0", "vue-tree-color": "^2.3.3", "vue2-editor": "^2.10.3", "vuedraggable": "^2.24.3", "vuex": "3.1.0", "vuex-persistedstate": "^4.1.0"}, "devDependencies": {"@babel/core": "^7.23.3", "@babel/eslint-parser": "^7.23.3", "@commitlint/cli": "^18.4.3", "@commitlint/config-conventional": "^18.4.3", "@vue/cli-plugin-babel": "4.4.4", "@vue/cli-plugin-eslint": "4.4.4", "@vue/cli-plugin-unit-jest": "4.4.4", "@vue/cli-service": "4.4.4", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "9.5.1", "babel-eslint": "10.1.0", "babel-jest": "23.6.0", "babel-plugin-dynamic-import-node": "2.3.3", "chalk": "2.4.2", "compression-webpack-plugin": "^4.0.0", "connect": "3.6.6", "cross-env": "^7.0.3", "crypto-js": "^4.1.1", "eslint": "^8.7.0", "eslint-config-ali": "^14.0.2", "eslint-config-hbis": "^2.3.10", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-filenames-simple": "^0.9.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-vue": "^9.33.0", "html-webpack-plugin": "3.2.0", "http-proxy-agent": "^7.0.2", "husky": "^8.0.0", "identity-obj-proxy": "^3.0.0", "less": "^3.9.0", "less-loader": "^4.1.0", "lint-staged": "13.1.2", "mockjs": "1.0.1-beta3", "patch-package": "^7.0.0", "runjs": "4.3.2", "sass": "1.26.8", "sass-loader": "8.0.2", "script-ext-html-webpack-plugin": "2.1.3", "serve-static": "1.13.2", "stylelint-config-hbis": "^2.0.5", "svg-sprite-loader": "4.1.3", "svgo": "1.2.2", "vue-eslint-parser": "^9.3.2", "vue-template-compiler": "2.6.10", "webpack-bundle-analyzer": "^4.9.0"}, "browserslist": ["> 1%", "last 2 versions"], "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "license": "MIT", "main": ".eslintrc.js", "directories": {"test": "tests"}, "repository": {"type": "git", "url": "http://***********:8000/sbdd/DevCenter/CommunityGroup/dangjianvue.git"}, "lint-staged": {"*.{js,ts,cjs,vue,html}": ["eslint --fix"]}}