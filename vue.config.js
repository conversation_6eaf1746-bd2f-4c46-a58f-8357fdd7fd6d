'use strict'
const path = require('path')
const defaultSettings = require('./src/framework/utils/settings.js')
// 开启gzip压缩， 按需引用
const CompressionPlugin = require('compression-webpack-plugin')

function resolve(dir) {
  return path.join(__dirname, dir)
}

const name = defaultSettings.title || 'vue Admin' // page title

// If your port is set to 80,
// use administrator privileges to execute the command line.
// For example, Mac: sudo npm run
// You can change the port by the following methods:
// port = 9528 npm run dev OR npm run dev --port = 9528
const port = process.env.port || process.env.npm_config_port || 9528 // dev port
const publicPath = process.env.VUE_APP_BUILD_APP === 'true' ? './' : (process.env.VUE_APP_PUBLIC_PATH || '/')
// const publicPath = './'

// All configuration item explanations can be find in https://cli.vuejs.org/config/
module.exports = {
  /**
   * You will need to set publicPath if you plan to deploy your site under a sub path,
   * for example GitHub Pages. If you plan to deploy your site to https://foo.github.io/bar/,
   * then publicPath should be set to "/bar/".
   * In most cases please use '/' !!!
   * Detail: https://cli.vuejs.org/config/#publicpath
   */
  publicPath, // 引入静态资源（js、css）时的基础路径
  outputDir: 'dist',
  assetsDir: 'static',
  // lintOnSave: process.env.NODE_ENV === 'development',
  lintOnSave: false,
  productionSourceMap: false,
  devServer: {
    port,
    open: true,
    overlay: {
      warnings: false,
      errors: true
    },
    // proxy: {
    //   [process.env.VUE_APP_BASE_API]: {
    //     target: process.env.VUE_APP_TARGET,
    //     changeOrigin: true,
    //     pathRewrite: {
    //       ['^' + process.env.VUE_APP_BASE_API]: '/dtap-cloud-app'
    //     }
    //   }
    // }

    proxy: {
      [process.env.VUE_APP_BASE_API + process.env.VUE_APP_MOCK_BASE_API]: {
        https: true,
        target: 'http://18.10.255.4:3000/',
        ws: true,
        changeOrigin: true,
        pathRewrite: {
          // [`^${process.env.VUE_APP_BASE_API}`]: '/'
        }
      },
      [process.env.VUE_APP_BASE_API]: {
        target: process.env.VUE_APP_TARGET,
        changeOrigin: true,
        pathRewrite: {
          [`^${process.env.VUE_APP_BASE_API}`]: '/'
        }
      }

    }

    // before: require('./mock/mock-server.js')
  },
  css: {
    loaderOptions: {
      sass: {
        sassOptions: { outputStyle: 'expanded' }
      }
    }
  },
  configureWebpack: {
    // provide the app's title in webpack's name field, so that
    // it can be accessed in index.html to inject the correct title.
    name,
    resolve: {
      alias: {
        '@': resolve('src')
      }
    },
    // externals: { //  打包时排除echarts  减少打包后体积
    //   'echarts': 'echarts'
    // },
    plugins: [
      // new CompressionPlugin({
      //   algorithm: 'gzip', // 使用gzip压缩
      //   test: /\.js$|\.html$|\.css$/, // 匹配文件名
      //   filename: '[path][base].gz[query]', // 压缩后的文件名(保持原文件名，后缀加.gz)
      //   minRatio: 1, // 压缩率小于1才会压缩
      //   threshold: 10240, // 对超过10k的数据压缩
      //   deleteOriginalAssets: false // 是否删除未压缩的源文件，谨慎设置，如果希望提供非gzip的资源，可不设置或者设置为false（比如删除打包后的gz后还可以加载到原始资源文件）
      // })
    ]
  },
  chainWebpack(config) {
    // it can improve the speed of the first screen, it is recommended to turn on preload
    config.plugin('preload').tap(() => [
      {
        rel: 'preload',
        // to ignore runtime.js
        // https://github.com/vuejs/vue-cli/blob/dev/packages/@vue/cli-service/lib/config/app.js#L171
        fileBlacklist: [/\.map$/, /hot-update\.js$/, /runtime\..*\.js$/],
        include: 'initial'
      }
    ])

    // when there are many pages, it will cause too many meaningless requests
    config.plugins.delete('prefetch')

    // config.plugin('webpack-bundle-analyzer')
    //   .use(require('webpack-bundle-analyzer').BundleAnalyzerPlugin)

    // 处理svg  loader framework目录以及外层目录
    // set svg-sprite-loader
    config.module
      .rule('svg')
      .exclude.add(resolve('src/framework/assets/icons'))
      .add(resolve('src/assets/icons'))
      .end()
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/framework/assets/icons'))
      .add(resolve('src/assets/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      })
      .end()

    config
      .when(process.env.NODE_ENV !== 'development',
        (config) => {
          config
            .plugin('ScriptExtHtmlWebpackPlugin')
            .after('html')
            .use('script-ext-html-webpack-plugin', [{
              // `runtime` must same as runtimeChunk name. default is `runtime`
              inline: /runtime\..*\.js$/
            }])
            .end()
          config
            .optimization.splitChunks({
              chunks: 'all',
              minSize: 0, // 体积为0时也进行拆分
              minChunks: 1, // 任何被引用过的模块都进行拆分
              maxAsyncRequests: 20, // 最大异步请求数
              maxInitialRequests: 20, // 最大初始请求数
              cacheGroups: {
                libs: {
                  name: 'chunk-libs',
                  test: /[\\/]node_modules[\\/]/,
                  priority: 10,
                  chunks: 'initial' // only package third parties that are initially dependent
                },
                elementUI: {
                  name: 'chunk-elementUI', // split elementUI into a single package
                  priority: 20, // the weight needs to be larger than libs and app or it will be packaged into libs or app
                  test: /[\\/]node_modules[\\/]_?element-ui(.*)/ // in order to adapt to cnpm
                },
                echartsLib: {
                  name: 'echarts-lib',
                  test: (module) => {
                    return /(echarts|zrender)/.test(module.context)
                  },
                  chunks: 'all',
                  priority: 20
                },
                'chunk-dev': {
                  name: 'chunk-dev',
                  test: resolve('src/framework/views/developer-center'), // can customize your rules
                  // minChunks: 3, //  minimum common number
                  priority: 5
                  // reuseExistingChunk: true
                },
                'chunk-system': {
                  name: 'chunk-system',
                  test: resolve('src/framework/views/system'), // can customize your rules
                  // minChunks: 3, //  minimum common number
                  priority: 5
                  // reuseExistingChunk: true
                },
                'chunk-assets': {
                  name: 'chunk-assets',
                  test: resolve('src/framework/assets'), // can customize your rules
                  // minChunks: 3, //  minimum common number
                  priority: 5
                  // reuseExistingChunk: true
                },
                tinymce: {

                  name: 'chunk-tinymce',
                  chunks: 'all',
                  test: (module) => {
                    return /(tinymce)/.test(module.context)
                  },
                  priority: 20
                  // reuseExistingChunk: true
                }
              }
            })
          // https:// webpack.js.org/configuration/optimization/#optimizationruntimechunk
          config.optimization.runtimeChunk('single')
        })
  }
}
