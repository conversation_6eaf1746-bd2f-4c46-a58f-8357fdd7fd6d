<template>
  <div v-if="pageType === 'list'" class="app-container">
    <div class="mainbox">
      <div class="filter-container">
        <el-form
          ref="queryForm"
          inline
          :model="queryParams"
          label-width="100px"
          @submit.native.prevent
        >
          <el-form-item label="培训名称">
            <el-input v-model="queryParams.trainName" />
          </el-form-item>

          <el-form-item label="培训方式">
            <el-select v-model="queryParams.online">
              <el-option
                v-for="item in trainingMethodOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="培训类型">
            <el-select v-model="queryParams.trainType" clearable>
              <el-option
                v-for="item in trainingTypeOptions"
                :key="item.id"
                :value="item.dictCode"
                :label="item.dictName"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="培训时间">
            <el-date-picker
              v-model="trainingTime"
              type="datetimerange"
              start-placeholder="请选择"
              end-placeholder="请选择"
              value-format="yyyy-MM-dd HH:mm:ss"
              clearable
            />
          </el-form-item>

          <el-form-item label="状态" width="80px" align="center">
            <el-select v-model="queryParams.status" clearable>
              <el-option
                v-for="item in isEnabledOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>

          <div class="fr">
            <el-button type="primary" icon="el-icon-search" @click="handleQuery">
              搜索
            </el-button>

            <el-button icon="el-icon-refresh" @click="handleReset">
              重置
            </el-button>
          </div>
        </el-form>
      </div>

      <div class="table-container table-fullscreen">
        <div class="table-opt-container">
          <el-button
            type="primary"
            icon="el-icon-plus"
            @click="handleAdd"
          >
            新增
          </el-button>

          <div class="flex-1" />
        </div>

        <el-table
          ref="table"
          v-loading="isLoading"
          style="width: 100%;"
          border
          highlight-current-row
          class="training-plan-table"
          row-key="id"
          :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
          :default-expand-all="false"
          :header-cell-style="{ backgroundColor: '#f2f2f2' }"
          :data="tableData"
        >
          <template slot="empty">
            <p>{{ $store.getters.dataText }}</p>
          </template>

          <el-table-column
            label="序号"
            align="left"
            width="80"
          >
            <template #default="scope">
              <span>{{ getRowIndex(scope.row) }}</span>
            </template>
          </el-table-column>

          <el-table-column
            key="trainName"
            label="培训名称"
            show-overflow-tooltip
            prop="trainName"
            width="130"
          >
            <template #default="scope">
              <span v-if="!scope.row.children" class="view-text" @click="handleViewDetail(scope.row)">
                {{ scope.row.trainName }}
              </span>

              <span v-else>
                {{ scope.row.trainName }}
              </span>
            </template>
          </el-table-column>

          <el-table-column
            key="online"
            label="培训方式"
            prop="online"
            :formatter="trainingMethodFormatter"
            show-overflow-tooltip
            width="90"
          />

          <el-table-column
            label="培训类型"
            prop="trainTypeName"
            show-overflow-tooltip
            width="140"
          />

          <el-table-column
            label="人员范围"
            prop="personnelScope"
            show-overflow-tooltip
            :formatter="personnelScopeFormatter"
            width="100"
          />

          <el-table-column
            label="培训人数"
            prop="planNumber"
            show-overflow-tooltip
            width="110"
          />

          <el-table-column
            label="培训时间"
            show-overflow-tooltip
            width="210"
            :formatter="trainingTimeFormatter"
          />

          <el-table-column
            label="状态"
            prop="status"
            show-overflow-tooltip
            :formatter="isEnabledFormatter"
            width="90"
          />

          <el-table-column
            label="创建人"
            prop="createName"
            show-overflow-tooltip
            width="110"
          />

          <el-table-column
            label="创建时间"
            prop="createTime"
            show-overflow-tooltip
            width="110"
          />

          <el-table-column
            label="是否重复"
            show-overflow-tooltip
            prop="repetition"
            :formatter="repetitionFormatter"
            width="110"
          />

          <el-table-column
            label="是否考试"
            show-overflow-tooltip
            prop="examType"
            :formatter="hasExamFormatter"
            width="110"
          />


          <el-table-column
            label="操作"
            align="left"
            class-name="small-padding fixed-width"
            width="230"
            fixed="right"
          >
            <template #default="scope">
              <TableActions
                v-if="!scope.row.children"
                :actions="tableActions"
                :scope="scope"
              />
            </template>
          </el-table-column>
        </el-table>

        <dt-pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNo"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
    </div>

    <EvaluationModal
      :visible.sync="isEvaluationModalVisible"
      :training-id="trainingId"
      :training-name="trainingName"
    />

    <StatisticModal
      :visible.sync="isStatisticModalVisible"
      :training-id="trainingId"
    />

    <OfflineRecordModal
      :visible.sync="isOfflineRecordModalVisible"
      :mode="offlineRecordModalMode"
      :training-id="trainingId"
    />
  </div>

  <TrainingPlanForm
    v-else
    :training-plan-id="currentTrainingPlanId"
    :mode="formMode"
    @back="handleBack"
  />
</template>

<script>
// TODO: 编辑、启用、删除，按钮状态 禁用
import { getTrainingPlanList, deleteTrainingPlan, updateTrainingPlanStatus } from '@/api/learning-manage/training-plan'
import {
  TRAINING_METHOD_OPTIONS,
  IS_ENABLED_OPTIONS,
  CONSTANTS,
  REPETITION_OPTIONS
} from '@/constants/training-plan'
import dayjs from 'dayjs'
import EvaluationModal from './components/evaluation-modal.vue'
import StatisticModal from './components/statistic-modal.vue'
import OfflineRecordModal from './components/offline-training-record-modal.vue'
import TableActions from '@/components/table-actions'
import TrainingPlanForm from './components/training-plan-form.vue'
export default {
  name: 'TrainingPlan',
  components: {
    EvaluationModal,
    StatisticModal,
    OfflineRecordModal,
    TableActions,
    TrainingPlanForm
  },

  data() {
    const tableActions = [
      {
        label: '数据统计',
        onClick: (scope) => this.handleViewStatistics(scope.row),
        visible: (scope) => {
          return scope.row.online === CONSTANTS.TRAINING_METHOD_ONLINE
        }
      },
      {
        label: '查看记录',
        onClick: (scope) => this.handleViewRecord(scope.row),
        visible: (scope) => {
          return scope.row.online === CONSTANTS.TRAINING_METHOD_OFFLINE
            && scope.row.finishStatus === CONSTANTS.FINISH_STATUS_FINISHED
        }
      },
      {
        label: '上传记录',
        onClick: (scope) => this.handleUploadRecord(scope.row),
        visible: (scope) => {
          return scope.row.online === CONSTANTS.TRAINING_METHOD_OFFLINE
            && scope.row.finishStatus === CONSTANTS.FINISH_STATUS_NOT_FINISHED
        }
      },
      {
        label: '培训评价',
        onClick: (scope) => this.handleViewEvaluation(scope.row)
      },
      {
        label: '考试详情',
        onClick: (scope) => this.handleViewExam(scope.row),
        visible: (scope) => scope.row.examType === CONSTANTS.HAS_EXAM
      },
      {
        label: '启用',
        onClick: (scope) => this.handleEnable(scope.row),
        visible: (scope) => scope.row.status === CONSTANTS.STATUS_DISABLED
      },
      {
        label: '停用',
        onClick: (scope) => this.handleDisable(scope.row),
        visible: (scope) => scope.row.status === CONSTANTS.STATUS_ENABLED
      },
      {
        label: '编辑',
        onClick: (scope) => this.handleEdit(scope.row),
        visible: (scope) => {
          if (dayjs().isAfter(dayjs(scope.row.trainBegin))) {
            return false
          } else {
            return scope.row.status === CONSTANTS.STATUS_DISABLED
              && scope.row.finishStatus !== CONSTANTS.FINISH_STATUS_FINISHED
          }
        }
      },
      {
        label: '删除',
        onClick: (scope) => this.handleDelete(scope.row),
        visible: (scope) => {
          if (dayjs().isAfter(dayjs(scope.row.trainBegin))) {
            return false
          } else {
            return scope.row.status === CONSTANTS.STATUS_DISABLED
              && scope.row.finishStatus !== CONSTANTS.FINISH_STATUS_FINISHED
          }
        }
      }
    ]

    return {
      pageType: 'list',
      // 常量对象
      CONSTANTS,
      // 培训评价模态框
      isEvaluationModalVisible: false,
      // 培训统计模态框
      isStatisticModalVisible: false,
      // 线下记录模态框
      isOfflineRecordModalVisible: false,
      // 培训计划id
      trainingId: '',
      offlineRecordModalMode: 'edit',
      trainingName: '',

      queryParams: {
        trainName: '',
        online: '',
        trainType: '',
        beginTime: '',
        endTime: '',
        status: '',
        pageNo: 1,
        pageSize: 10
      },

      // 培训方式
      trainingMethodOptions: TRAINING_METHOD_OPTIONS,
      // 是否启用
      isEnabledOptions: IS_ENABLED_OPTIONS,

      tableActions,
      total: 0,
      isLoading: false,
      tableData: [],
      trainingTypeOptions: [],

      currentTrainingPlanId: '',
      formMode: 'add'
    }
  },

  computed: {
    trainingTime: {
      set(value) {
        if (value && value.length === 2) {
          this.queryParams.beginTime = value[0]
          this.queryParams.endTime = value[1]
        } else {
          this.queryParams.beginTime = ''
          this.queryParams.endTime = ''
        }
      },

      get() {
        if (this.queryParams.beginTime && this.queryParams.endTime) {
          return [this.queryParams.beginTime, this.queryParams.endTime]
        } else {
          return []
        }
      }
    }
  },

  watch: {},
  created() {
    this.handleQuery()
    this.getDicts()
  },

  mounted() {
  },

  methods: {
    handleBack(refreshOnBack = false) {
      if (refreshOnBack) {
        this.getList()
      }
      this.pageType = 'list'
    },

    getRowIndex(row) {
      const isChild = row.groupId !== '0'
      const offset = (this.queryParams.pageNo - 1) * this.queryParams.pageSize
      if (isChild) {
        const parentIndex = this.tableData.findIndex((item) => item.id === row.groupId)
        const childIndex = this.tableData[parentIndex].children.findIndex((item) => item.id === row.id)
        return `${offset + parentIndex + 1}.${childIndex + 1}`
      } else {
        const index = this.tableData.findIndex((item) => item.id === row.id)
        return offset + index + 1
      }
    },

    handleAdd() {
      this.currentTrainingPlanId = ''
      this.formMode = 'add'
      this.pageType = 'form'
    },

    handleViewDetail(row) {
      this.currentTrainingPlanId = row.id
      this.formMode = 'view'
      this.pageType = 'form'
    },

    handleViewStatistics(row) {
      this.isStatisticModalVisible = true
      this.trainingId = row.id
    },

    handleViewRecord(row) {
      this.isOfflineRecordModalVisible = true
      this.trainingId = row.id
      this.trainingName = row.trainName
      this.offlineRecordModalMode = 'view'
    },


    handleUploadRecord(row) {
      this.isOfflineRecordModalVisible = true
      this.trainingId = row.id
      this.trainingName = row.trainName
      this.offlineRecordModalMode = 'edit'
    },

    handleViewEvaluation(row) {
      this.isEvaluationModalVisible = true
      this.trainingId = row.id
      this.trainingName = row.trainName
    },

    handleViewExam(row) {
      this.$router.push(`/organizeExam?id=${row.id}`)
    },

    handleEnable(row) {
      this.$confirm('确定启用该培训计划吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        updateTrainingPlanStatus({ id: row.id, status: CONSTANTS.STATUS_ENABLED })
          .then(() => {
            this.$message.success('启用成功')
            this.getList()
          })
      })
    },

    handleDisable(row) {
      this.$confirm('确定停用该培训计划吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        updateTrainingPlanStatus({ id: row.id, status: CONSTANTS.STATUS_DISABLED })
          .then(() => {
            this.$message.success('停用成功')
            this.getList()
          })
      })
    },

    handleDelete(row) {
      this.$confirm('确定删除该培训计划吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteTrainingPlan({ id: row.id })
          .then(() => {
            this.$message.success('删除成功')
            this.getList()
          })
          .catch(() => {
            this.$message.error('删除失败')
          })
      })
    },

    handleEdit(row) {
      this.currentTrainingPlanId = row.id
      this.formMode = 'edit'
      this.pageType = 'form'
    },

    getDicts() {
      this.businessDictList({ dictTypeCode: 'trainType' }).then((res) => {
        this.trainingTypeOptions = res.data.rows
      })
    },

    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },

    handleReset() {
      this.queryParams = {
        orgId: this.$store.getters.orgId,
        planYear: `${dayjs().year()}`,
        trainType: '',
        completeStatus: '',
        delayStatus: '',
        searchBeginTime: '',
        searchEndTime: '',
        pageNo: 1,
        pageSize: 10
      }
      this.getList()
    },

    transformListData(listData) {
      if (!listData || listData.length === 0) {
        return []
      }
      return listData.map((item) => {
        if (item.repetitionList) {
          return {
            ...item,
            children: item.repetitionList
          }
        } else {
          return { ...item }
        }
      })
    },

    getList() {
      const loadData = (data) => {
        this.tableData = this.transformListData(data.data.rows)
        this.total = data.data.totalRows
      }

      this.isLoading = true
      getTrainingPlanList(this.queryParams)
        .then(loadData)
        .catch((err) => {
          this.$message.error('获取培训计划列表失败')
          console.log('err,', err)
        })
        .finally(() => {
          this.isLoading = false
        })
    },

    trainingMethodFormatter(row, column, cellValue) {
      const item = this.trainingMethodOptions.find((item) => item.value === cellValue)
      return item ? item.label : cellValue
    },

    getRepetitionLabel(row) {
      let label
      const weekmap = ['日', '一', '二', '三', '四', '五', '六'].map(
        (label) => `周${label}`
      )
      const item = REPETITION_OPTIONS.find((item) => item.value === row.repetition)

      if (!row.trainBegin) {
        label = item.label
      } else if (item.value === CONSTANTS.REPETITION_NONE) {
        label = '不重复'
      } else if (item.value === CONSTANTS.REPETITION_WEEKLY) {
        label = `${item.label}（${
          weekmap[dayjs(row.trainBegin).day()]
        }）`
      } else {
        label = `${item.label}（${dayjs(row.trainBegin).date()}日）`
      }
      return label
    },

    trainingTimeFormatter(row) {
      if (row.children) {
        return `${row.trainBegin}~${this.getRepetitionLabel(row)}`
      } else {
        return `${row.trainBegin}~${row.trainEnd}`
      }
    },

    isEnabledFormatter(row, column, cellValue) {
      const item = this.isEnabledOptions.find((item) => item.value === cellValue)
      return item ? item.label : cellValue
    },

    repetitionFormatter(row, column, cellValue) {
      return cellValue === CONSTANTS.REPETITION_NONE ? '不重复' : '重复'
    },

    hasExamFormatter(row, column, cellValue) {
      return cellValue === CONSTANTS.HAS_EXAM ? '是' : '否'
    },

    personnelScopeFormatter(row, column, cellValue) {
      return cellValue === CONSTANTS.PERSONNEL_SCOPE_EXTERNAL ? '相关方人员' : '公司人员'
    }
  }
}
</script>

<style scoped lang="scss">
.view-text {
  color: var(--primary);
  cursor: pointer;
  font-weight: 500;
}
.training-plan-table {
  // 对齐树状表格中没有子节点行的第一列单元格
  ::v-deep td:first-child {
    .el-table__indent + .el-table__placeholder {
      width: 0;
    }
  }
  // 对齐树状表格中没有子节点行的第一列单元格
  ::v-deep .el-table__body-wrapper tr:not([class*="el-table__row--level"]) {
    td:first-child .cell {
      padding-left: 39px !important;
    }
  }
}
</style>
