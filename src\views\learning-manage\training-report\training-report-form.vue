<template>
  <div class="training-plan-form">
    <div class="title-box">
      <div class="title">
        详情
      </div>

      <div class="title-actions">
        <el-button type="" @click="handleBack">
          返回
        </el-button>
      </div>
    </div>

    <div class="section-title">培训内容</div>

    <table>
      <tbody>
        <tr>
          <td>培训名称</td>
          <td>{{ formData.trainName }}</td>
          <td>培训类型</td>
          <td>{{ getOptionName("trainingType", formData.trainType) }}</td>
          <td>培训方式</td>
          <td>{{ getOptionName("trainingMethod", formData.online) }}</td>
        </tr>
        <tr>
          <td>人员范围</td>
          <td>{{ getOptionName("personnelScope", formData.personnelScope) }}</td>
          <td>培训讲师</td>
          <td v-if="formData.trainForm === 'inner'">{{ formData.teacher || "--" }}</td>
          <td v-else>{{ formData.outTeacher || "--" }}</td>
          <td>创建部门</td>
          <td>{{ formData.orgName }}</td>
        </tr>
        <tr>
          <td>是否考试</td>
          <td>{{ getOptionName("examType", formData.examType) }}</td>
          <td>培训人数</td>
          <td>{{ formData.planNumber }}</td>
          <td>实际培训人数</td>
          <td>{{ formData.totalStuff }}</td>
        </tr>
        <tr>
          <td>培训时间</td>
          <td>{{ formatTrainingTime(formData.trainBegin,formData.trainEnd) }}</td>
          <td>实际培训时间</td>
          <td>{{ formatTrainingTime(formData.realBeginTime,formData.realEndTime) }}</td>
          <td v-if="formData.online == 2">培训地点</td>
          <td v-if="formData.online == 2">{{ formData.position }}</td>
        </tr>
        <tr>
          <td>培训课程</td>
          <td colspan="5" style="text-align:left;">{{ formData.courseName }}</td>
        </tr>
      </tbody>
    </table>

    <div class="section-title">效果评价</div>

        <div class="mainbox">
      <div class="filter-container">
        <el-form
          ref="queryForm"
          inline
          :model="queryParams"
          label-width="100px"
          @submit.native.prevent
        >
          <el-form-item label="姓名">
            <el-input v-model="queryParams.userName" placeholder="请输入姓名" />
          </el-form-item>
          <el-form-item label="部门">
            <DeptSelect v-model="queryParams.deptId" placeholder="请选择部门" />
          </el-form-item>

          <div class="fr">
            <el-button type="primary" icon="el-icon-search" @click="handleQuery">
              搜索
            </el-button>

            <el-button icon="el-icon-refresh" @click="handleReset">
              重置
            </el-button>
          </div>
        </el-form>
      </div>

      <div class="table-container table-fullscreen">
        <div class="table-opt-container">
          <div class="flex-1" />
        </div>

        <el-table
          ref="table"
          v-loading="isLoading"
          style="width: 100%;"
          border
          :row-style="{ height: '60px' }"
          :header-cell-style="{ backgroundColor: '#f2f2f2' }"
          :data="tableData"
        >
          <!-- <template slot="empty">
            <p>{{ $store.getters.dataText }}</p>
          </template> -->

          <el-table-column
            type="index"
            label="序号"
            width="70"
            :index="
              (index) =>
                (queryParams.pageNo - 1) * queryParams.pageSize + index + 1
            "
          />

          <el-table-column
            key="userName"
            label="姓名"
            show-overflow-tooltip
            prop="userName"
            width="150"
          >
          </el-table-column>

          <el-table-column
            key="orgName"
            label="部门"
            prop="orgName"
            show-overflow-tooltip
            width="180"
          />

          <el-table-column
            label="是否完成"
            prop="finishStatus"
            show-overflow-tooltip
            :formatter="finishStatusFormatter"
            width="100"
          />

          <el-table-column
            label="是否考试"
            prop="examType"
            show-overflow-tooltip
            :formatter="examTypeFormatter"
            width="100"
          />

          <el-table-column
            label="考试成绩"
            prop="userScore"
            show-overflow-tooltip
            width="110"
          />

          <el-table-column
            label="培训效果"
            show-overflow-tooltip
            width="0"
            prop="trainingEffect"
          />

          <el-table-column
            label="确认签字"
            prop="signature"
            min-width="100"
          >
            <template slot-scope="scope">
              <div class="signature-container" v-if="scope.row.signature">
                <PreviewImage :file-id="scope.row.signature" />
              </div>
            </template>
          </el-table-column>

        </el-table>

        <dt-pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNo"
          :limit.sync="queryParams.pageSize"
          @pagination="getTrainingRememberPage"
        />
      </div>
    </div>

    <div class="section-title">员工满意度</div>
    <div class="statistics-container">
      <div class="statistics-info">
        <div class="info-item">
          <div class="info-label">实际参加人数</div>
          <div class="info-value">{{ statisticsData.totalStuff }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">评价人数</div>
          <div class="info-value">{{ statisticsData.evaluatTotal }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">整体满意率</div>
          <div class="info-value highlight">{{ statisticsData.satisfactionLevel }}%</div>
        </div>
      </div>
      <div class="chart-container">
        <div id="trainingReportFormMain"></div>
      </div>
    </div>

  </div>
</template>

<script>
import dayjs from 'dayjs'
import {
  // 选项数组
  TRAINING_METHOD_OPTIONS,
  TRAINING_FORM_OPTIONS,
  REPETITION_OPTIONS,
  REPETITION_TYPE_OPTIONS,
  IS_ENABLED_OPTIONS,
  HAS_EXAM_OPTIONS,
  EXAM_MODE_OPTIONS,
  PERSONNEL_SCOPE_OPTIONS,
  // 常量值对象
  CONSTANTS
} from '@/constants/training-plan'
import {
  getTrainingPlanById, getTrainingPlanEvaluation, getPageByTrainingId
} from '@/api/learning-manage/training-report.js'
import DeptSelect from '@/components/dept-select/dept-select.vue'
import PreviewImage from "@/components/preview-image/preview-image.vue";
import * as echarts from 'echarts'

export default {
  name: 'TrainingReportForm',
  components: { DeptSelect,PreviewImage },
  props: {
    currentId: {
      type: String,
      required: true
    }
  },
  data() {

    return {
      trainingId: '',
      CONSTANTS,
      trainingTypeOptions: [],
      // 培训方式选项
      trainingMethodOptions: TRAINING_METHOD_OPTIONS,
      // 培训形式选项
      trainingFormOptions: TRAINING_FORM_OPTIONS,
      // 重复类型选项
      repetitionOptions: REPETITION_OPTIONS,
      // 重复结束类型选项
      repetitionTypeOptions: REPETITION_TYPE_OPTIONS,
      // 是否启用选项
      isEnabledOptions: IS_ENABLED_OPTIONS,
      // 考试类型选项
      hasExamOptions: HAS_EXAM_OPTIONS,
      // 考试方式选项
      examModeOptions: EXAM_MODE_OPTIONS,
      // 人员范围选项
      personnelScopeOptions: PERSONNEL_SCOPE_OPTIONS,
      isLoading: false,

      queryParams: {
        userName: '',
        deptId: '',
        trainingId: '',
        pageNo: 1,
        pageSize: 10
      },
      total: 0,
      tableData: [],

      formData: {
        id: '',
        createTime: '',
        createUser: '',
        updateTime: '',
        updateUser: '',
        createName: '',
        orgName: '',
        ownerOrgId: null,
        trainName: '',
        trainType: '',
        online: '', // 默认线上培训，对应 TRAINING_METHOD_OPTIONS
        trainBegin: '',
        trainEnd: '',
        // 重复类型
        repetition: '', // 默认不重复，对应 REPETITION_OPTIONS
        repetitionType: '', // 默认某天结束，对应 REPETITION_TYPE_OPTIONS
        // 重复次数
        repetitions: '',
        // 重复结束日期
        repetitionEndDate: '',
        // 培训课程
        onlineCourseList: [],
        // 培训课时
        hours: 0,
        // 培训地点
        position: '',
        // 培训形式
        trainForm: '',
        // 培训老师（内培时使用）
        teacher: '',
        // 外培老师
        outTeacher: '',
        // 人员范围
        personnelScope: '',
        // 培训人员
        userIdList: [],
        // 培训人员预览
        remembers: [],
        // 备注
        remark: '',
        // 是否考试
        examType: '', // 默认考试
        status: '', // 默认停用
        trainingExamPlanRequest: {
          // 选择试卷
          paperId: '',
          // 考试名称
          examName: '',
          // 考试方式
          examMode: '', // 默认线上考试
          // 考试开始时间
          examBeginTime: '',
          // 考试结束时间
          examEndTime: '',
          // 限时
          examTime: '', // 考试时长，单位分钟
          examLocation: '',
          // 及格线
          passScore: '',
          // 限制次数
          limitTimes: 1,
          // 备注
          remark: ''
        }
      },
      statisticsData: {
        totalStuff: 0,
        evaluatTotal: 0,
        verySatisfiedTotal: 0,
        satisfiedTotal: 0,
        acceptableTotal: 0,
        noSatisfiedTotal: 0,
        veryDissatisfiedTotal: 0,
        satisfactionLevel: 0
      },
      // personSelectorApi: {
      //   getManagingOrgUserList,
      //   searchUserName,
      //   getUserListByUserIds,
      //   searchOrgName
      // }
    }
  },

  computed: {
  },

  watch: {
  },

  created() {
    this.getDicts()
    this.initFormData()
  },

  methods: {
    examTypeFormatter(row, column, cellValue) {
      return cellValue === CONSTANTS.HAS_EXAM ? '是' : '否'
    },
    finishStatusFormatter(row, column, cellValue) {
      return cellValue === CONSTANTS.FINISH_STATUS_FINISHED ? '是' : '否'
    },
    formatTrainingTime(trainBegin,trainEnd) {
      if (!trainBegin || !trainEnd) return '--'

      const start = dayjs(trainBegin).format('YYYY-MM-DD HH:mm:ss')
      const end = dayjs(trainEnd).format('YYYY-MM-DD HH:mm:ss')
      return `${start} ~ ${end}`
    },
    getOptionName(name, value) {
      let options = []
      switch (name) {
        case 'trainingType':
          options = this.trainingTypeOptions
          break
        case 'trainingMethod':
          options = this.trainingMethodOptions
          break
        case 'trainingForm':
          options = this.trainingFormOptions
          break
        case 'personnelScope':
          options = this.personnelScopeOptions
          break
        case 'examType':
          options = this.hasExamOptions
          break
        case 'examMode':
          options = this.examModeOptions
          break
        default:
          return '--'
      }

      if (name === 'trainingType') {
        return (
          options.find((item) => item.dictCode === value)?.dictName || '--'
        )
      }
      return options.find((item) => item.value === value)?.label || '--'
    },

    handleRepetitionTypeChange() {
      this.formData.repetitions = 1
      this.formData.repetitionEndDate = ''
    },

    handleRepetitionChange() {
      this.formData.repetitions = 1
      this.formData.repetitionEndDate = ''
      this.formData.repetitionType = CONSTANTS.REPETITION_TYPE_DATE
    },

    handleTrainingFormChange() {
      this.formData.outTeacher = ''
      this.formData.teacher = []
    },

    handleOnlineChange() {
      this.formData.hours = 0
      this.formData.position = ''
      this.formData.onlineCourseList = []
    },

    handleTrainingTimeChange() {
      this.formData.repetition = CONSTANTS.REPETITION_NONE
      this.formData.repetitionType = CONSTANTS.REPETITION_TYPE_DATE
      this.formData.repetitions = ''
      this.formData.repetitionEndDate = ''
    },

    

    getDicts() {
      this.businessDictList({ dictTypeCode: 'trainType' }).then((res) => {
        this.trainingTypeOptions = res.data.rows
      })
    },

    initFormData() {
      // const id = this.$route.query.id
      // 查看，根据id获取详情
      if (this.currentId) {
        this.getDetail(this.currentId)
      } else {
        this.$message.error('获取数据失败')
      }
      
    },

    getDetail(id) {
      this.trainingId = id
      this.isLoading = true
      getTrainingPlanById(id).then((res) => {
          this.formData = res.data
          this.getTrainingRememberPage()
          this.getTrainingEvaluationInfo()
      }).catch((err) => {
          this.$message.error('获取培训内容失败')
          console.error(err)
      }).finally(() => {
          this.isLoading = false
      })
    },

    getTrainingRememberPage(){
      this.queryParams.trainingId = this.trainingId
      getPageByTrainingId(this.queryParams).then((res) => {
          this.tableData = res.data.rows
          this.total = res.data.totalRows
      }).catch((err) => {
          this.$message.error('获取效果评价失败')
          console.error(err)
      })
    },

    handleQuery() {
      this.queryParams.pageNo = 1
      this.getTrainingRememberPage()
    },

    handleReset() {
      this.queryParams = {
        deptId: '',
        trainingId: this.trainingId,
        userName: '',
        pageNo: 1,
        pageSize: 10
      }
      this.getTrainingRememberPage()
    },

    getTrainingEvaluationInfo(){
      getTrainingPlanEvaluation(this.trainingId).then((res) => {
          if(res.data){
            this.statisticsData = res.data
            this.initChart()
          }
      }).catch((err) => {
          this.initChart()
          this.$message.error('获取效果评价失败')
          console.error(err)
      })
    },

    initChart(){
      const chartDom = document.getElementById('trainingReportFormMain')
      const myChart = echarts.init(chartDom)

      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },

        legend: {},
        // grid: {
        //   left: '30%',
        //   right: "4%",
        //   bottom: '3%',
        //   containLabel: true
        // },

        xAxis: [
          {
            type: 'category',
            data: ['很满意（人）', '满意（人）', '尚可（人）', '不满意（人）', '很不满意（人）'],
            axisTick: {
              alignWithLabel: true
            }
          }
        ],

        yAxis: [
          {
            type: 'value',
            name: '人数',
            minInterval: 1 // 保证坐标轴刻度为整数
          }
        ],

        series: [
          {
            name: '人数',
            type: 'bar',
            barWidth: 80,
            data: [
              this.statisticsData.verySatisfiedTotal,
              this.statisticsData.satisfiedTotal,
              this.statisticsData.acceptableTotal,
              this.statisticsData.noSatisfiedTotal,
              this.statisticsData.veryDissatisfiedTotal
            ],
            itemStyle: {
              color: '#0e76c7'
            }
          },
        ]
      }

      option && myChart.setOption(option)

    },

    handleBack() {
      this.$emit('back')
      // 关闭当前页
    },

  }
}
</script>

<style lang="scss" scoped>
.training-plan-form {
  padding: 20px;
  background-color: #fff;

  .title-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ebeef5;

    .title {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }

    .title-actions {
      .el-button {
        margin-left: 10px;
      }
    }
  }

  .course-list {
    &.view {
      .course-item {
        padding: 8px 12px;
        margin-bottom: 8px;
        background-color: #f5f7fa;
        border-radius: 4px;
        color: #303133;
      }
    }

    .course-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 12px;
      margin-bottom: 8px;
      background-color: #f5f7fa;
      border-radius: 4px;

      span {
        color: #303133;
      }
    }
  }

  .table-actions {
    margin-bottom: 10px;
    text-align: right;

    .el-button {
      margin-left: 10px;
    }
  }
}
  body {
    font-family: "Microsoft YaHei", Arial, sans-serif;
    margin: 20px;
  }
  .section-title {
    background: #f5f5f5;
    padding: 10px 15px;
    font-weight: bold;
    margin: 20px 0 10px 0;
    border-radius: 3px;
    font-size: 20px;
  }
  table {
    width: 100%;
    border-collapse: collapse;
    table-layout: fixed;
    word-wrap: break-word;
  }
  table, th, td {
    border: 1px solid #ccc;
  }
  th, td {
    padding: 30px 10px;
    text-align: center;
    vertical-align: middle;
    font-size: 16px;
  }
  /* 灰色背景列 */
  td:nth-child(1),
  td:nth-child(3),
  td:nth-child(5),
  th:nth-child(1),
  th:nth-child(3),
  th:nth-child(5) {
    background-color: #f5f5f5;
  }

  .table-con{
    min-height: 205px;
  }

.statistics-container {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  
  .statistics-info {
    display: flex;
    gap: 40px;
    margin-bottom: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 6px;
    
    .info-item {
      flex: 1;
      text-align: center;
      
      .info-label {
        color: #606266;
        font-size: 14px;
        margin-bottom: 8px;
      }
      
      .info-value {
        font-size: 24px;
        font-weight: 600;
        color: #303133;
        
        &.highlight {
          color: #409EFF;
        }
      }
    }
  }

  .chart-container {
    height: 442px;
    border-radius: 6px;
    background: #fff;
    padding: 20px;
    border: 1px solid #ebeef5;
    
    #trainingReportFormMain {
      width: 100%;
      height: 100%;
    }
  }
}

.signature-container {
  height: 50px !important;      /* 固定高度 */
  display: flex;                /* 启用Flex布局 */
  align-items: center;          /* 垂直居中 */
  justify-content: center;      /* 水平居中 */
  overflow: hidden;             /* 隐藏超出部分 */
}

/* 控制内部图片样式 */
// .signature-container img {
//   max-height: 100%;             /* 限制图片最大高度 */
//   max-width: 100%;              /* 限制图片最大宽度 */
//   object-fit: contain;          /* 保持比例完整显示 */
// }
</style>
