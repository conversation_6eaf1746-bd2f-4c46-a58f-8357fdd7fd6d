<template>
  <AliyunPlayerModal
    :visible="isModalVisible"
    title="视频播放检测"
    width="300px"
  >
    <p class="tip">
      点击播放，继续观看视频
    </p>

    <div class="activity-check-action">
      <el-button
        @click="handleContinue"
      >
        取消
      </el-button>

      <el-button
        type="primary"
        @click="handleContinue"
      >
        播放
      </el-button>
    </div>
  </AliyunPlayerModal>
</template>

<script>
import AliyunPlayerModal from '@/components/aliyun-player-modal'

export default {
  name: 'ActivityCheckModal',
  components: {
    AliyunPlayerModal
  },

  inheritAttrs: false,

  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {}
  },

  computed: {
    isModalVisible: {
      get() {
        return this.visible
      },

      set(val) {
        return this.$emit('update:visible', val)
      }
    }
  },

  methods: {
    handleContinue() {
      this.$emit('continue', true)
      this.$emit('update:visible', false)
    },

    handleCancel() {
      this.$emit('continue', false)
      this.$emit('update:visible', false)
    }
  }
}
</script>

<style lang="scss" scoped>
.activity-check-action {
  display: flex;
  justify-content: flex-end;
  gap: 10px;

}
.tip {
  margin-top: 0;
  margin-bottom: 20px;
}
</style>
