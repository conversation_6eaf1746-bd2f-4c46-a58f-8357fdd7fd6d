<template>
  <div>
    <UploadAuth
      v-if="!disabled"
      ref="uploadAuthRef"
      @file-change="handleFileChange"
      @upload-start="handleUploadStart"
      @upload-succeed="handleUploadSucceed"
    />

    <AliyunPlayer
      v-if="videoUrl"
      :key="videoUrl"
      ref="playRef"
      class="preview-player"
      :source="videoUrl"
      :config="config"
    />
  </div>
</template>

<script>
import AliyunPlayer from '@/components/aliyun-player'
import merge from 'lodash.merge'
import UploadAuth from './upload-auth.vue'
import { getPlayInfo } from '@/api/aliyun-vod'

export default {
  name: 'AliyunUpload',
  components: { AliyunPlayer, UploadAuth },
  props: {
    playerConfig: {
      type: Object,
      default: () => ({})
    },

    disabled: {
      type: Boolean,
      default: false
    },

    value: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
      uploadProgress: 0,
      localDataUrl: '',
      remoteDataUrl: ''
    }
  },

  computed: {
    videoUrl() {
      if (this.disabled) {
        return this.remoteDataUrl
      } else {
        return this.localDataUrl || this.remoteDataUrl
      }
    },

    config() {
      return merge({
        controlBarVisibility: 'always',
        width: '100%',
        autoSize: 'height'
      }, this.playerConfig)
    }
  },

  watch: {
    localDataUrl(newURL, oldURL) {
      if (oldURL) {
        URL.revokeObjectURL(oldURL)
      }
    },

    value: {
      handler(val, oldVal) {
        // localDataUrl 存在时，不请求远程播放地址
        if (!val || this.localDataUrl) return
        this.getPlaybackUrl()
      },

      immediate: true
    }
  },

  created() {
  },

  mounted() {
  },

  methods: {
    getPlaybackUrl() {
      getPlayInfo(this.value)
        .then((res) => {
          console.log('playinfo', res.data.body)
          const list = res.data.body.playInfoList.playInfo
          if (!list || !list.length) {
            this.$message.error('获取视频播放地址失败')
            return
          }
          console.log('videoURL', list[0].playURL)
          this.remoteDataUrl = list[0].playURL
          console.log('remote video duration', list[0].duration)
          this.$emit('duration-loaded', parseFloat(list[0].duration))
        })
        .catch((err) => {
          this.$message.error('获取播放地址失败')
          console.log(err)
        })
    },

    handleUploadStart() {
      this.$emit('upload-start')
    },

    handleUploadSucceed(uploadInfo) {
      this.$emit('upload-succeed')
      this.$emit('input', uploadInfo.videoId)
    },


    handleFileChange(file) {
      if (this.localDataUrl) {
        URL.revokeObjectURL(this.localDataUrl)
      }

      this.localDataUrl = URL.createObjectURL(file)
      this.getVideoDuration(this.localDataUrl)
    },

    getVideoDuration(dataUrl) {
      let video = document.createElement('video')
      video.src = dataUrl
      video.addEventListener('loadedmetadata', () => {
        console.log('local video duration', video.duration)
        this.$emit('duration-loaded', video.duration)
        video = null
      }, { once: true })
    },

    handleRequestFullScreen(event) {
      console.log(event, '请求全屏')
    },

    handleCancelFullScreen(event) {
      console.log(event, '取消全屏')
    },

    reset() {
      if (this.localDataUrl) {
        URL.revokeObjectURL(this.localDataUrl)
        this.localDataUrl = ''
      }
      if (this.$refs.uploadAuthRef) {
        this.$refs.uploadAuthRef.reset()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.auth-upload-container {
  margin-bottom: 10px;
}
</style>
