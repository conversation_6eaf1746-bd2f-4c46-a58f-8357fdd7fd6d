<!--
  * @Author: wangzexin <EMAIL>
  * @Date: 2024-07-04 16:12:04
  * @LastEditors: duhongyan <EMAIL>
  * @LastEditTime: 2024-10-23 13:58:24
  * @Description: 我的培训列表页
  * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved.
-->
<template>
  <div class="app-container">
    <div class="mainbox">
      <div class="filter-container">
        <el-form
          ref="queryForm"
          :model="queryParams"
          :inline="true"
          label-width="100px"
          @submit.native.prevent
        >
          <el-form-item label="培训名称" prop="trainName">
            <el-input
              v-model.trim="queryParams.trainName"
              class="filter-item limit"
              style="width: 200px; margin-right: 10px"
              maxlength="30"
              clearable
              placeholder="请输入培训名称"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>

          <el-form-item label="培训方式" prop="online">
            <el-select v-model="queryParams.online" clearable placeholder="请选择">
              <el-option label="线上" value="1"/>
              <el-option label="线下" value="2"/>
            </el-select>
          </el-form-item>

          <el-form-item label="培训类型" prop="trainType">
            <el-select v-model="queryParams.trainType" clearable placeholder="请选择">
              <el-option
                v-for="item in trainingTypeOptions"
                :key="item.dictCode"
                :label="item.dictName"
                :value="item.dictCode"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="培训时间">
            <el-date-picker
              v-model="daterange"
              type="daterange"
              clearable
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd"
            />
          </el-form-item>
        </el-form>

        <div class="flex-1"/>

        <div class="fr">
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">
            搜索
          </el-button>

          <el-button icon="el-icon-refresh" @click="handleReset">
            重置
          </el-button>
        </div>
      </div>

      <div class="table-container table-fullscreen">
        <div class="table-opt-container">
          <div class="flex-1"/>
        </div>

        <el-table
          ref="table"
          v-loading="loading"
          style="width: 100%;"
          border
          highlight-current-row
          :header-cell-style="{ backgroundColor: '#f2f2f2' }"
          :data="tableData"
        >
          <template slot="empty">
            <p>{{ $store.getters.dataText }}</p>
          </template>

          <el-table-column
            type="index"
            label="序号"
            width="70"
            :index="
              (index) =>
                (queryParams.pageNo - 1) * queryParams.pageSize + index + 1
            "
          />

          <el-table-column
            key="trainName"
            label="培训名称"
            show-overflow-tooltip
            align="center"
            prop="trainName"
            min-width="200"
          />

          <el-table-column
            key="online"
            label="培训方式"
            show-overflow-tooltip
            align="center"
            prop="online"
            min-width="120"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.online == '1'">
                线上
              </span>

              <span v-else-if="scope.row.online == '2'">
                线下
              </span>
            </template>
          </el-table-column>

          <el-table-column
            label="培训类型"
            show-overflow-tooltip
            align="center"
            prop="trainTypeName"
            min-width="120"
          />

          <el-table-column
            label="人员范围"
            show-overflow-tooltip
            align="center"
            prop="personnelScope"
            min-width="200"
          >
            <template slot-scope="scope">
              <span>
                {{ scope.row.personnelScope == '1' ? '公司人员' : '相关方人员' }}
              </span>
            </template>
          </el-table-column>

          <el-table-column
            label="是否考试"
            show-overflow-tooltip
            align="center"
            prop="examType"
            min-width="200"
          >
            <template slot-scope="scope">
              <span>
                {{ scope.row.examType == 1 ? '是' : '否' }}
              </span>
            </template>
          </el-table-column>

          <el-table-column
            label="培训人数"
            show-overflow-tooltip
            align="center"
            prop="planNumber"
            min-width="120"
          />

          <el-table-column
            key="trainBegin"
            label="培训时间"
            show-overflow-tooltip
            align="center"
            prop="trainBegin"
            min-width="240"
          >
            <template slot-scope="scope">
              <span>
                {{ scope.row.trainBegin }}~{{ scope.row.trainEnd }}
              </span>
            </template>
          </el-table-column>

          <el-table-column
            label="实际培训人数"
            show-overflow-tooltip
            align="center"
            prop="totalStuff"
            min-width="120"
          />

          <el-table-column
            key="realBeginTime"
            label="实际培训时间"
            show-overflow-tooltip
            align="center"
            prop="trainBegin"
            min-width="240"
          >
            <template slot-scope="scope">
              <span>
                {{ scope.row.realBeginTime }}~{{ scope.row.realEndTime }}
              </span>
            </template>
          </el-table-column>

          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
            min-width="100"
            fixed="right"
          >
            <template slot-scope="scope">
              <el-button
                type="text"
                @click="affirm(scope.row)"
              >
                效果确认
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <dt-pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNo"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
    </div>
    <!-- 查看积分明细弹框 -->
    <el-dialog title="员工培训效果确认" :visible.sync="dialogVisible">
      <div class="filter-container">
        <el-form
          ref="queryForm"
          :model="detailParams"
          :inline="true"
          label-width="100px"
          @submit.native.prevent
        >
          <el-form-item label="签字状态" prop="isSign">
            <el-select v-model="detailParams.isSign" clearable placeholder="请选择">
              <el-option label="已签字" value="1"/>
              <el-option label="未签字" value="0"/>
            </el-select>
          </el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="selectTrainingUserList">
            搜索
          </el-button>
        </el-form>
        <div class="flex-1"/>
        <div class="fr">

          <el-button :disabled="!multiple" type="primary" @click="openSign(2)">
            签字确认
          </el-button>
        </div>
      </div>
      <div>
        <el-table v-loading="dialogLoading" ref="dialogTable" border highlight-current-row
                  :header-cell-style="{ backgroundColor: '#f2f2f2'}" :data="trainingUserList"
                  @selection-change="handleSelectionChange"
        >
          <el-table-column
            fixed="left"
            type="selection"
            width="55"
            align="center"
          />
          <el-table-column
            type="index"
            label="序号"
            width="70"
            :index="
              (index) =>
                (queryParams.pageNo - 1) * queryParams.pageSize + index + 1
            "
          />

          <el-table-column
            label="姓名"
            show-overflow-tooltip
            align="center"
            prop="userName"
            min-width="200"
          />

          <el-table-column
            label="部门"
            show-overflow-tooltip
            align="center"
            prop="orgName"
            min-width="110"
          />
          <el-table-column
            label="是否完成"
            show-overflow-tooltip
            align="center"
            prop="isEnd"
            min-width="120"
          >
            <template slot-scope="scope">
              <span>
                {{ scope.row.finishStatus == 1 ? '是' : '否' }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            label="是否考试"
            show-overflow-tooltip
            align="center"
            prop="isExam"
            min-width="120"
          >
            <template slot-scope="scope">
              <span>
                {{ scope.row.examType == 1 ? '是' : '否' }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            label="考试成绩"
            show-overflow-tooltip
            align="center"
            prop="userScore"
            min-width="120"
          />
          <el-table-column
            label="培训效果"
            align="center"
            min-width="100"
          >
            <template slot-scope="scope">
              <el-select v-if="detailParams.isExam == 0" v-model="scope.row.trainingEffect" @change="changeTrainEffect(scope.row)">
                <el-option label="不具备该项技能" value="不具备该项技能"/>
                <el-option label="能在他人指导下工作" value="能在他人指导下工作"/>
                <el-option label="能独立开展工作" value="能独立开展工作"/>
                <el-option label="能熟练的独立工作" value="能熟练的独立工作"/>
                <el-option label="能熟练的独立工作，并能培训他人" value="能熟练的独立工作，并能培训他人"/>
              </el-select>
              <span v-else>{{ scope.row.trainingEffect }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="确认签字"
            align="center"
            min-width="100"
          >
            <template slot-scope="scope">
              <PreviewImage v-if="scope.row.signature" :file-id="scope.row.signature" />
            </template>
          </el-table-column>


        </el-table>
        <dt-pagination
          v-show="detailParams.total>0"
          :total="detailParams.total"
          :page.sync="detailParams.pageNo"
          :limit.sync="detailParams.pageSize"
          @pagination="getTrainingUserList"
        />
      </div>
      <dt-dialog title="签名管理" :visible.sync="signOpen" :is-loading="signLoading"
                 @comfirmBtn="submitSign" @closeDialog="cancelSign">
        <div slot="content">
          <el-form ref="form" label-width="130px">
            <el-form-item v-if="signType === 1" label-width="80px" label="上传签名" prop="appName">
              <FileUploadEcho
                style="width: 100%;"
                :show-preview="true"
                :file-id.sync="sign"
                :file-limit="1"
                :file-size="5"
                :is-show-tip="true"
                :file-type="['png','jpg','jpeg']"
              />
            </el-form-item>
            <el-form-item v-if="signType === 2" label-width="80px" label="手写签名" prop="appName">
              <vue-esign ref="esign" style="border: 2px dotted #ccc " :width="800" :height="300" :isCrop="signSetting.isCrop" :lineWidth="signSetting.lineWidth" :lineColor="signSetting.lineColor" :bgColor.sync="signSetting.bgColor" :isClearBgColor="false" />
              <el-button type="primary" size="mini" @click="resetSign()">清空画板</el-button>
              <!--                <el-button type="primary" size="mini" @click="handleGenerate()">生成图片</el-button>-->
            </el-form-item>
          </el-form>
        </div>
      </dt-dialog>
    </el-dialog>
  </div>
</template>

<script>
import moment from "moment"
import {
  batchEditTrainingRemember,
  editTrainingRemember, getExamTotalScore,
  getTrainingUserListPage,
  getTrainRecordPage
} from '@/api/training-examination/my-training.js'
import {getFileDetail} from '@/api/common/index'
import {title} from "@/framework/utils/settings";
import {encryption} from "DQBasic-vue-component";
import {checkPhoneOrEmailOnly} from "@/framework/api/userCenter/sysUser";
import {cloud} from "@/framework/utils/request";
import store from "@/store";
import {getPlanInfoList} from "@/api/training-examination/training-paln";
import uploadIcon from "@/framework/views/system/applicationMag/components/uploadIcon.vue";
import FileUploadEcho from "@/components/file-upload-echo/file-upload-echo.vue";
import DtUpdatePw from "@/framework/views/system/user/components/dt-updatePw/index.vue";
import VueEsign from "vue-esign";
import PictureUploadEcho from "@/components/picture-upload-echo/picture-upload-echo.vue";
import PreviewImage from "@/components/preview-image/preview-image.vue";
import request  from '@/framework/utils/request'

export default {
  components: { uploadIcon, FileUploadEcho, DtUpdatePw, VueEsign, PictureUploadEcho, PreviewImage },
  data() {
    return {
      // 查询参数表单
      queryParams: {
        trainName: '',
        online: '',
        trainType: '',
        searchBeginTime: '',
        searchEndTime: '',
        pageNo: 1,
        pageSize: 10
      },

      query: {},
      // 培训类型下拉选
      trainingTypeOptions: [],
      trainingUserList: [],
      // 培训方式下拉选
      trainingMethodsOptions: [
        {
          value: '0',
          label: '全部'
        }, {
          value: '1',
          label: '线上'
        }, {
          value: '2',
          label: '线下'
        }
      ],
      single: false,
      multiple: false,

      // 控制查看弹框
      viewDialogVisible: false,
      // 查询参数日期范围
      daterange: '',
      // 表格loading
      loading: true,
      // 表格数据源
      tableData: [],
      // 表格总条数
      total: 0,
      // 新增、查看弹框标题
      dialogTitle: '',
      formData: {},
      // 弹框类型
      type: '',
      // 新增、查看弹框是否显示
      dialogVisible: false,
      dialogLoading: false,
      detailParams: {
        trainingId: '',
        pageNo: 1,
        pageSize: 10,
        total: 0,
        isSign: '',
        isExam: 0,
        totalScore: 0
      },
      sign: '',
      signOpen: false,
      signLoading: false,
      signType: '',
      resultImg:'',
      selection: [],
      signSetting:{
        lineWidth: 6,
        lineColor: '#000000',
        bgColor: '',
        resultImg: '',
        isCrop: false
      },
    }
  },

  computed: {},

  watch: {},
  created() {
    this.businessDictList({dictTypeCode: 'trainType'}).then(res => {
      this.trainingTypeOptions = res.data.rows
    })
    this.handleQuery()
  },

  mounted() {

  },

  beforeDestroy() {
  },

  methods: {
    title() {
      return title
    },
    /** 查询方法 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()

    },

    /** 重置方法 */
    handleReset() {
      this.queryParams = {
        trainName: '',
        online: '',
        trainType: '',
        searchBeginTime: '',
        searchEndTime: '',
        pageNo: 1,
        pageSize: 10
      }
      this.daterange = ''
      this.getList()
    },

    resetSign() {
      this.$refs.esign.reset()
    },

    /** 查询方法 */
    getList() {
      this.query = JSON.parse(JSON.stringify(this.queryParams))
      if (this.daterange != '' && this.daterange != null) {
        this.query.searchBeginTime = this.daterange[0]
        this.query.searchEndTime = this.daterange[1]
      } else {
        this.query.searchBeginTime = null
        this.query.searchEndTime = null
      }
      this.query.userId = this.$store.getters.userId
      this.loading = true
      getTrainRecordPage(this.query).then(data => {
        this.loading = false
        this.tableData = data.data.rows
        this.total = data.data.totalRows
      })
    },

    /** 效果确认 */
    affirm(row) {
      this.type = 'add'
      this.dialogTitle = '培训人员列表'
      this.dialogVisible = true
      this.detailParams.trainingId = row.id
      this.detailParams.isSign = ''
      this.detailParams.isExam = row.examType
      this.dialogLoading = true
      this.trainingUserList = []
      getExamTotalScore({trainId: row.id}).then(res => {
        this.detailParams.totalScore = res.data
        this.selectTrainingUserList()
      })
    },

    changeTrainEffect(row){
      editTrainingRemember(row).then(res => {
        this.$message.success('保存成功')
      })
    },

    changeSign(value) {

    },

    selectTrainingUserList(){
      this.detailParams.pageNo = 1
      this.detailParams.pageSize = 10
      this.getTrainingUserList()
    },

    getTrainingUserList() {
      this.dialogLoading = true
      getTrainingUserListPage(this.detailParams).then(res => {
        this.dialogLoading = false
        this.detailParams.total = res.data.totalRows
        this.trainingUserList = res.data.rows
        if (this.detailParams.isExam == 1){
          this.trainingUserList.forEach(item => {
            if ((item.trainingEffect == null || item.trainingEffect == '')&& item.userScore != null ){
              if (item.userScore * 100 < this.detailParams.totalScore * 60){
                item.trainingEffect = '不具备该项技能'
              } else if (item.userScore * 100 < this.detailParams.totalScore * 70){
                item.trainingEffect = '能在他人指导下工作'
              } else if (item.userScore * 100 < this.detailParams.totalScore * 80){
                item.trainingEffect = '能独立开展工作'
              } else if (item.userScore * 100 < this.detailParams.totalScore * 90){
                item.trainingEffect = '能熟练的独立工作'
              }else {
                item.trainingEffect = '能熟练的独立工作，并能培训他人'
              }
            }
          })
        }
      })
    },

    // 多选框选中数据
    handleSelectionChange(selection) {
      this.formData.ids = selection.map((item) => item.id)
      this.selection  = selection
      this.single = selection.length !== 1
      let multiplef = !selection.length
      this.multiple = !multiplef
    },


    /** 更新新增弹框状态 */
    updateDialogVisible(val) {
      this.dialogVisible = val
      this.getList()
    },
    /** 格式化表格列时间 */
    dateFormat(row, column, cellValue) {
      // 假设 cellValue 是一个毫秒数或标准的 JavaScript Date 对象
      return moment(cellValue).format('YYYY-MM-DD HH:mm:ss') // 使用 Moment.js
      // 或者
      return new Date(cellValue).toLocaleString() // 使用原生 JavaScript
    },

    openSign(type) {
      let canSign = true
      this.selection.forEach(item => {
        if (item.trainingEffect == null || item.trainingEffect == ''){
          canSign = false
        }
      })
      if (!canSign) {
        this.$message.error(this.detailParams.isExam == 0?'请先选择培训效果':'请选择完成考试的考生')
        return
      }
      this.signOpen = true
      this.signType = type
    },

    cancelSign() {
      this.signOpen = false
    },

    submitSign() {
      this.signLoading = true
      if (this.signType === 1) {
        this.submitSignChange()
      } else if (this.signType === 2) {
        this.handleGenerate()
      }
    },

    handleGenerate() {
      this.sign = ''
      this.$refs.esign.generate().then(res => {
        const file = this.dataURLtoFile(
          res,
          `upload_${Date.now()}.${res.split(';')[0].split('/')[1]}`
        );
        // 创建FormData对象并添加文件
        const data = new FormData();
        data.append('file', file);
        request({
          url: '/project/bizFileInfo/put-file',
          method:'post',
          data,
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        }).then((re) => {
          this.sign = re.data.id
          this.submitSignChange()
        })

      }).catch(err => {
        alert(err) // 画布没有签字时会执行这里 'Not Signned'
      })
    },

    // 将Base64转换为File对象（因为后端通常接收文件对象）
    dataURLtoFile(dataurl, filename) {
      const arr = dataurl.split(',');
      const mime = arr[0].match(/:(.*?);/)[1];
      const bstr = atob(arr[1]);
      let n = bstr.length;
      const u8arr = new Uint8Array(n);

      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      return new File([u8arr], filename, { type: mime });
    },

    async checkOnly(rule, value) {
      const form = {}
      form[rule.field] = value
      form.userId = this.formData.userId
      const temp = encryption({
        data: form
      })
      const { message } = await checkPhoneOrEmailOnly(temp)
      if (message == '') {
        return true
      } else {
        return false
      }
    },

    submitSignChange() {
      let list = []
      this.selection.forEach(item => {
        list.push({
          id: item.id,
          trainingEffect: item.trainingEffect,
          signature: this.sign
        })
      })
      batchEditTrainingRemember({list: list}).then(res => {
        this.signOpen = false
        this.$message.success('保存成功')
        this.selectTrainingUserList()
      })
    },
  }
}
</script>


