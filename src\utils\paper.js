export function getTotalScore(paper) {
  const judgeCount = parseInt(paper.judgeCount)
  const multipleChoiceCount = parseInt(paper.multipleChoiceCount)
  const singleChoiceCount = parseInt(paper.singleChoiceCount)
  const judgeScore = parseFloat(paper.judgeScore)
  const multipleChoiceScore = parseFloat(paper.multipleChoiceScore)
  const singleChoiceScore = parseFloat(paper.singleChoiceScore)

  const arr = [
    [judgeCount, judgeScore],
    [multipleChoiceCount, multipleChoiceScore],
    [singleChoiceCount, singleChoiceScore]
  ]

  // 如果score或count为NaN，则返回0
  return arr.reduce((totalScore, [score, count]) => {
    if (isNaN(score) || isNaN(count)) {
      return totalScore + 0
    } else {
      return totalScore + score * count
    }
  }, 0)
}

export function getTotalQuestion(paper) {
  const judgeCount = parseInt(paper.judgeCount)
  const multipleChoiceCount = parseInt(paper.multipleChoiceCount)
  const singleChoiceCount = parseInt(paper.singleChoiceCount)

  const arr = [judgeCount, multipleChoiceCount, singleChoiceCount]

  return arr.reduce((totalQuestion, count) => {
    if (isNaN(count)) {
      return totalQuestion + 0
    } else {
      return totalQuestion + count
    }
  }, 0)
}

export function getFormattedUseTime(totalSeconds) {
  const hours = Math.floor(totalSeconds / 3600)
  const remainingSeconds = totalSeconds % 3600
  const minutes = Math.floor(remainingSeconds / 60)
  const seconds = remainingSeconds % 60

  let result = `${seconds}秒`
  if (minutes > 0) {
    result = `${minutes}分${result}`
  }
  if (hours > 0) {
    result = `${hours}时${result}`
  }
  return result
}
