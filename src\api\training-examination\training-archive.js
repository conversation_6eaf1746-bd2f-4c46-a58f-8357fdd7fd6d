/*
 * @Author: wangzexin <EMAIL>
 * @Date: 2024-07-15 17:13:13
 * @LastEditors: wangzexin <EMAIL>
 * @LastEditTime: 2024-07-16 16:19:31
 * @Description: 查看考试
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved.
 */
import request from '@/framework/utils/request'
import { cloud } from '@/framework/utils/request'
// 获取用户信息
export function getUserInfo() {
  return request({
    url: `${cloud.usercenter}/sysUser/currentUserInfo`,
    method: 'get'
  })
}
export function getCurrentInfo() {
  return request({
    url: `${cloud.dqbasic}/personal-basic/currentInfo`,
    method: 'get'
  })
}

export function addPersonalArchive(params) {
  return request({
    url: `${cloud.dqbasic}/trainingArchive/add`,
    method: 'post',
    data: params
  })
}

export function editPersonalArchive(params) {
  return request({
    url: `${cloud.dqbasic}/trainingArchive/edit`,
    method: 'post',
    data: params
  })
}

/**
 * 获取当前登录用户的个人档案详情
 * @returns 获取用户档案详情
 */
export function getPersonalArchiveDetail() {
  return request({
    url: `${cloud.dqbasic}/trainingArchive/detail`,
    method: 'get'
  })
}

export function exportPreview(isPreview) {
  return request({
    url: `${cloud.dqbasic}/trainingArchive/exportPreview?isPreview=`+isPreview,
    method: 'get',
    responseType: 'blob',
  })
}

/**
 * 获取班组级岗位安全教育
 * @param {string} userId - 受教育人的用户ID
 * @param {string} trainId - 培训计划的基本信息ID
 * @returns
 */
export function getTrainGroupDetail(userId, trainId) {
  return request({
    url: `${cloud.dqbasic}/trainingArchive/getTrainGroupDetail`,
    method: 'get',
    params: { userId, trainId }
  })
}


/**
 * 包教师傅打分-签字打分提交
 * @param {object} params - 签字和打分信息
 * @returns 
 */
export function submitMasterScore(params) {
  return request({
    url: `${cloud.dqbasic}/trainingArchive/submitMasterScore`,
    method: 'post',
    data: params
  })
}


/**
 * 推送消息通知给包教师傅进行签字
 * @param {string} trainId - 培训计划-基本信息id
 * @returns 
 */
export function pushSignNoticeToMaster(trainId) {
  return request({
    url: `${cloud.dqbasic}/trainingArchive/pushSignNoticeToMaster`,
    method: 'post',
    data: {trainId}
  })
}
