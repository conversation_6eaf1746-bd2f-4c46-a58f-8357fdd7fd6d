<template>
  <div class="app-container">
    <div class="mainbox">
      <div class="filter-container">
        <el-form
          ref="queryForm"
          :model="queryParams"
          :inline="true"
          label-width="70px"
          style="width: 100%;display: flex;align-items: center;justify-content: space-between;"
          @submit.native.prevent
        >
          <el-form-item label="考试名称" prop="examName">
            <el-input
              v-model.trim="queryParams.examName"
              maxlength="30"
              placeholder="请输入"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>

          <el-form-item label="考试时间" prop="examTimeRange">
            <el-date-picker
              v-model="queryParams.examTimeRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              clearable
            />
          </el-form-item>

          <el-form-item label="状态" prop="status">
            <el-select
              v-model="queryParams.status"
              placeholder="请选择"
              clearable
            >
              <el-option label="未开始" value="0" />

              <el-option label="进行中" value="1" />

              <el-option label="已结束" value="2" />
            </el-select>
          </el-form-item>

          <div class="fr">
            <el-button type="primary" icon="el-icon-search" @click="handleQuery">
              搜索
            </el-button>

            <el-button icon="el-icon-refresh" @click="handleReset">
              重置
            </el-button>
          </div>
        </el-form>
      </div>

      <div class="table-container table-fullscreen">
        <div class="table-opt-container">
          <el-button
            type="primary"
            icon="el-icon-plus"
            @click="handleAdd"
          >
            新增
          </el-button>

          <el-button
            type="primary"
            @click="showExamLink"
          >
            相关方考试链接
          </el-button>


          <div class="flex-1" />
        </div>

        <el-table
          ref="table"
          v-loading="loading"
          style="width: 100%;"
          border
          highlight-current-row
          :header-cell-style="{ backgroundColor: '#f2f2f2' }"
          :data="tableData"
        >
          <template slot="empty">
            <p>{{ $store.getters.dataText }}</p>
          </template>

          <el-table-column
            type="index"
            label="序号"
            width="70"
            :index="
              (index) =>
                (queryParams.pageNo - 1) * queryParams.pageSize + index + 1
            "
          />

          <el-table-column
            label="考试名称"
            show-overflow-tooltip
            align="center"
            prop="examName"
            min-width="180"
          >
            <template slot-scope="scope">
              <span style="color: #409EFF;cursor:pointer;" @click="handleDetail(scope.row)">
                {{ scope.row.examName }}
              </span>
            </template>
          </el-table-column>

          <el-table-column
            label="考试方式"
            show-overflow-tooltip
            align="center"
            prop="examMode"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.examMode == 1">
                线上考试
              </span>

              <span v-else-if="scope.row.examMode == 2">
                线下考试
              </span>
            </template>
          </el-table-column>

          <el-table-column
            label="人员范围"
            show-overflow-tooltip
            align="center"
            prop="personnelScope"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.personnelScope == 1">
                公司人员
              </span>

              <span v-else-if="scope.row.personnelScope == 2">
                相关方人员
              </span>
            </template>
          </el-table-column>

          <el-table-column
            label="考试类型"
            show-overflow-tooltip
            align="center"
            prop="examTypeName"
          />

          <el-table-column
            label="开始时间"
            show-overflow-tooltip
            align="center"
            prop="examBeginTime"
            min-width="120"
          />

          <el-table-column
            label="结束时间"
            show-overflow-tooltip
            align="center"
            prop="examEndTime"
            min-width="120"
          />

          <el-table-column
            label="限时(分钟)"
            align="center"
            prop="examTime"
          />

          <el-table-column
            label="状态"
            show-overflow-tooltip
            align="center"
            prop="status"
            min-width="60"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.status == 0">
                未开始
              </span>

              <span v-else-if="scope.row.status == 1">
                进行中
              </span>

              <span v-else-if="scope.row.status == 2">
                已结束
              </span>
            </template>
          </el-table-column>

          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
            min-width="140"
            fixed="right"
          >
            <template slot-scope="scope">
              <el-button
                v-if="scope.row.status == 0 || (scope.row.examMode == 2 && scope.row.status == 1)"
                type="text"
                @click="handleEdit(scope.row)"
              >
                编辑
              </el-button>

              <el-button
                v-if="scope.row.status == 0 || (scope.row.examMode == 2 && scope.row.status == 1)"
                type="text"
                @click="handleDelete(scope.row)"
              >
                删除
              </el-button>

              <el-button
                v-if="scope.row.examMode == 1 && scope.row.status == 1"
                type="text"
                @click="hanldeEnd(scope.row)"
              >
                结束
              </el-button>

              <el-button
                v-if="scope.row.examMode == 1 && (scope.row.status == 1 || scope.row.status == 2)"
                type="text"
                @click="showStatistics(scope.row)"
              >
                统计表
              </el-button>

              <el-button
                v-if="scope.row.examMode == 2 && scope.row.status == 1"
                type="text"
                @click="showExamResult(scope.row)"
              >
                上传结果
              </el-button>

              <el-button
                v-if="scope.row.status == 2"
                type="text"
                @click="showExamResult(scope.row)"
              >
                查看结果
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <dt-pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNo"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
    </div>

    <!-- 新增编辑详情弹框 -->
    <EditDialog ref="editRef" @update="handleQuery" />

    <!-- 考试链接弹框 -->
    <ExamLinkDialog ref="examLinkRef" />

    <!-- 统计表弹框 -->
    <StatisticTableDialog ref="statisticTableRef" />

    <!-- 线上考试结果弹框 -->
    <OnlineResultDialog ref="onlineResultRef" />

    <!-- 线下考试结果弹框 -->
    <OfflineResultDialog ref="offlineResultRef" @update="getList" />
  </div>
</template>

<script>
import {
  getTrainingExamPlanPage,
  trainingExamPlanDelete,
  trainingExamPlanEnd
} from '@/api/exam-manage/organize-exam'
import EditDialog from './components/edit-dialog.vue'
import ExamLinkDialog from './components/exam-link-dialog.vue'
import StatisticTableDialog from './components/statistic-table-dialog.vue'
import OnlineResultDialog from './components/online-result-dialog.vue'
import OfflineResultDialog from './components/offline-result-dialog.vue'

export default {
  name:'OrganizeExam',
  components: {
    EditDialog,
    ExamLinkDialog,
    StatisticTableDialog,
    OnlineResultDialog,
    OfflineResultDialog
  },

  data() {
    return {
      queryParams:{
        examName:'', // 考试名称
        examTimeRange:[], // 考试时间
        beginTime:'', // 考试开始时间
        endTime:'', // 考试结束时间
        status:'', // 状态
        pageNo:1,
        pageSize:10
      },

      loading:false,
      tableData:[],
      total:0
    }
  },

  created() {
    this.handleQuery()
  },

  methods: {
    // 复制考试链接
    showExamLink() {
      this.$refs.examLinkRef.init()
    },

    // 查询
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },

    // 重置
    handleReset() {
      this.queryParams = {
        examName:'', // 考试名称
        examTimeRange:[], // 考试时间
        beginTime:'', // 考试开始时间
        endTime:'', // 考试结束时间
        status:'', // 状态
        pageNo:1,
        pageSize:10
      }
      this.getList()
    },

    // 获取列表
    getList() {
      const query = JSON.parse(JSON.stringify(this.queryParams))
      if (!!query.examTimeRange && query.examTimeRange.length > 0) {
        query.beginTime = query.examTimeRange[0]
        query.endTime = query.examTimeRange[1]
      } else {
        query.beginTime = ''
        query.endTime = ''
      }
      this.loading = true
      getTrainingExamPlanPage(query).then((data) => {
        this.loading = false
        this.tableData = data.data.rows
        this.total = data.data.totalRows
      }).catch(() => {
        this.loading = false
      })
    },

    // 新增
    handleAdd() {
      this.$refs.editRef.init()
    },

    // 编辑
    handleEdit(row) {
      this.$refs.editRef.init(JSON.parse(JSON.stringify(row)))
    },

    // 详情
    handleDetail(row) {
      this.$refs.editRef.disabled = true
      this.$nextTick().then(() => {
        this.$refs.editRef.init(JSON.parse(JSON.stringify(row)))
      })
    },

    // 删除
    handleDelete(row) {
      this.$dtModal
        .confirm(`是否确认删除组织考试名称为"${row.examName}"的数据项？`)
        .then(() => {
          row.examIds = []
          row.examIds.push(row.examId)
          return trainingExamPlanDelete(row)
        })
        .then((res) => {
          if (res.success == true) {
            this.getList()
            this.$dtModal.msgSuccess('删除成功')
          }
        })
        .catch(() => {})
    },

    // 结束
    hanldeEnd(row) {
      this.$dtModal
        .confirm(`是否确认结束组织考试名称为"${row.examName}"的数据项？`)
        .then(() => {
          return trainingExamPlanEnd({ examId: row.examId })
        })
        .then((res) => {
          if (res.success == true) {
            this.getList()
            this.$dtModal.msgSuccess('结束成功')
          }
        })
        .catch(() => {})
    },

    // 统计表
    showStatistics(row) {
      this.$refs.statisticTableRef.init(JSON.parse(JSON.stringify(row)))
    },

    // 查看结果
    showExamResult(row) {
      if (row.examMode == 1) {
        this.$refs.onlineResultRef.init(JSON.parse(JSON.stringify(row)))
      } else if (row.examMode == 2) {
        this.$refs.offlineResultRef.init(JSON.parse(JSON.stringify(row)))
      }
    }
  }
}
</script>
