<template>
  <div class="app-container">
    <div class="mainbox">
      <div class="filter-container">
        <el-form
          ref="queryForm"
          :model="queryParams"
          :inline="true"
          label-width="70px"
          style="width: 100%;display: flex;align-items: center;justify-content: space-between;"
          @submit.native.prevent
        >
          <el-form-item label="姓名" prop="userName">
            <el-input
              v-model.trim="queryParams.userName"
              maxlength="30"
              placeholder="请输入"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>

          <el-form-item label="部门" prop="orgId">
            <DeptSelect v-model="queryParams.orgId" placeholder="请选择" />
          </el-form-item>

          <el-form-item label="考试名称" prop="examName">
            <el-input
              v-model.trim="queryParams.examName"
              maxlength="30"
              placeholder="请输入"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>

          <div class="fr">
            <el-button type="primary" icon="el-icon-search" @click="handleQuery">
              搜索
            </el-button>

            <el-button icon="el-icon-refresh" @click="handleReset">
              重置
            </el-button>
          </div>
        </el-form>
      </div>

      <div class="table-container table-fullscreen">
        <div class="table-opt-container">
          <el-button type="primary" icon="el-icon-download" @click="exportOut">
            导出
          </el-button>

          <div class="flex-1" />
        </div>

        <el-table
          ref="table"
          v-loading="loading"
          style="width: 100%;"
          border
          highlight-current-row
          :header-cell-style="{ backgroundColor: '#f2f2f2' }"
          :data="tableData"
        >
          <template slot="empty">
            <p>{{ $store.getters.dataText }}</p>
          </template>

          <el-table-column
            type="index"
            label="序号"
            width="70"
            :index="
              (index) =>
                (queryParams.pageNo - 1) * queryParams.pageSize + index + 1
            "
          />

          <el-table-column
            label="姓名"
            show-overflow-tooltip
            align="center"
            prop="userName"
            min-width="100"
          />

          <el-table-column
            label="部门"
            show-overflow-tooltip
            align="center"
            prop="orgName"
            min-width="120"
          />

          <el-table-column
            label="岗位"
            show-overflow-tooltip
            align="center"
            prop="positionName"
            min-width="120"
          />

          <el-table-column
            label="考试名称"
            show-overflow-tooltip
            align="center"
            prop="examName"
            min-width="350"
          />

          <el-table-column
            label="考试时间"
            show-overflow-tooltip
            align="center"
            prop="examBeginTime"
            min-width="320"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.examBeginTime }} ~ {{ scope.row.examEndTime }}</span>
            </template>
          </el-table-column>


          <el-table-column
            label="限制次数"
            align="center"
            prop="limitTimes"
            min-width="100"
          />

          <el-table-column
            label="答题次数"
            align="center"
            prop="examCount"
            min-width="100"
          />

          <el-table-column
            label="最高分"
            align="center"
            prop="highestScore"
            min-width="100"
          />

          <el-table-column
            label="答题时间"
            show-overflow-tooltip
            align="center"
            prop="startTime"
            min-width="320"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.startTime }} ~ {{ scope.row.endTime }}</span>
            </template>
          </el-table-column>

          <el-table-column
            label="答题用时(分钟)"
            align="center"
            prop="examTime"
            min-width="140"
          />
        </el-table>

        <dt-pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNo"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
    </div>
  </div>
</template>

<script>
import {
  getExamStatisticsPage,
  exportExamStatistics
} from '@/api/exam-manage/exam-statistics'
import DeptSelect from '@/components/dept-select/dept-select.vue'

export default {
  name:'ExamStatistics',
  components:{ DeptSelect },

  data() {
    return {
      // 表格
      loading: false,
      tableData: [],
      total: 0,
      queryParams:{
        userName:'', // 姓名
        orgId:'', // 部门
        examName:'', // 考试名称
        pageNo:1,
        pageSize:10
      }
    }
  },

  created() {
    this.handleQuery()
  },

  methods: {
    // 查询
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },

    // 重置
    handleReset() {
      this.queryParams = {
        allUserName:'', // 姓名
        orgName:'', // 部门
        examName:'', // 考试名称
        pageNo:1,
        pageSize:10
      }
      this.getList()
    },

    // 获取列表
    getList() {
      const query = JSON.parse(JSON.stringify(this.queryParams))
      this.loading = true
      getExamStatisticsPage(query).then((data) => {
        this.loading = false
        this.tableData = data.data.rows
        this.total = data.data.totalRows
      }).catch(() => {
        this.loading = false
      })
    },

    // 导出
    exportOut() {
      const data = JSON.parse(JSON.stringify(this.queryParams))
      exportExamStatistics(data).then((res) => {
        const blob = new Blob([res.data], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        })
        const objectUrl = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.style.display = 'none'
        a.href = objectUrl // 指定下载链接
        a.download = '考试统计' // 指定下载文件名
        a.click()
        URL.revokeObjectURL(a.href) // 释放URL对象
      })
    }
  }
}
</script>
