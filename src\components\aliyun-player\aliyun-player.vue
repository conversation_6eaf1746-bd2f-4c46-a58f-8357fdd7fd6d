<template>
  <div
    id="aliPrismPlayer"
    ref="playerRef"
    class="aliyun-player-vue"
    @mousemove="handleMouseMove"
  >
    <div class="slot-container">
      <slot :player="player" />
    </div>

    <transition name="message">
      <div
        v-if="isMessageVisible"
        class="message-box"
        :class="messageBoxClass"
        @mouseenter="resetMessageTimer"
        @mouseleave="startMessageTimer"
      >
        {{ message }}
      </div>
    </transition>
  </div>
</template>

<script>
// 播放器API文档 https://help.aliyun.com/zh/vod/developer-reference/api-operations
import Aliplayer from 'aliyun-aliplayer'
import 'aliyun-aliplayer/build/skins/default/aliplayer-min.css'
import { license, skinLayout } from './config'
import { EVENTS } from './constants'
import { kebabToCamel } from '@/utils/caseConverter.js'
import merge from 'lodash.merge'

/**
 * 播放器触发的事件，可通过 `@player-[event-name]` 的形式监听，注意是小写短横线kebab-case。
 * 完整事件名称参考：
 * https://help.aliyun.com/zh/vod/developer-reference/api-operations#section-d6l-d84-gwa
 * 参数为
 * {
 *   player: object // 播放器实例
 *   args: any // 播放器事件参数，详情请参考上方的文档
 * }
 */
export default {
  props: {
    isForwardSeekAllowed: {
      type: Boolean,
      default: true
    },

    source: {
      type: String,
      default: ''
    },

    config: {
      type: Object,
      default: () => ({})
    },

    maxPlaybackTime: {
      type: Number,
      default: () => 0
    }
  },

  data() {
    return {
      player: null,
      // 最大已播放时长
      isPlayerReady: false,
      // 播放器继续播放所需的“最小冗余秒数”，为了阻止用户快进而又不阻止播放器自动播放
      minAutoPlayOffset: 0.001,
      message: '',
      messageTimer: '',
      messageDuration: 3000,
      isMessageVisible: false,
      fullScreenTimer: '',
      messageBoxPosition: ['x-middle', 'y-start']
    }
  },

  computed: {
    messageBoxClass() {
      return this.messageBoxPosition.map((position) => `message-box--pos-${position}`)
    }
  },

  watch: {
    source(val) {
      if (!this.isPlayerReady || !val) return

      this.player.loadByUrl(val)
    },

    isForwardSeekAllowed(isAllowed) {
      if (!this.isPlayerReady) return

      const maxForwardSeekTime = isAllowed
        ? this.player.getDuration()
        : this.maxPlaybackTime
      this.player.setPreviewTime(maxForwardSeekTime + this.minAutoPlayOffset)
    }
  },

  mounted() {
    this.cleanup = this.setupPlayer()
  },

  beforeDestroy() {
    this.cleanup()
  },

  methods: {
    handleMouseMove() {
      // 修复全屏下无法看到底部控制栏的问题
      if (!this.player) return
      const isFullScreen = this.player.fullscreenService.getIsFullScreen()
      if (isFullScreen) {
        this.$el.querySelector('.prism-controlbar').style.display = 'block'
        clearTimeout(this.fullScreenTimer)
        this.fullScreenTimer = setTimeout(() => {
          this.$el.querySelector('.prism-controlbar').style.display = 'none'
        }, 5000)
      }
    },

    handleCancelFullScreen() {
      clearTimeout(this.fullScreenTimer)
    },

    getPlayerConfig() {
      return merge({
        id: 'aliPrismPlayer',
        source: this.source,
        preload: true,
        autoplay: false,
        // 自动循环播放
        rePlay: false,
        license,
        skinLayout
        // language: '鸟语',
        // languageTexts
      }, this.config)
    },

    setupPlayer() {
      const config = this.getPlayerConfig()
      console.log('merged config', config)
      this.player = new Aliplayer(config)
      this.unbindEventHandlers = this.bindEventHandlers()
      this.forwardMethod()

      return () => {
        this.unbindEventHandlers()
        this.player.dispose()
      }
    },

    startMessageTimer() {
      this.messageTimer = setTimeout(() => {
        this.isMessageVisible = false
        this.message = ''
      }, this.messageDuration)
    },

    resetMessageTimer() {
      clearTimeout(this.messageTimer)
      this.messageTimer = ''
    },

    showMessage(message, {
      duration,
      position
    } = {
      duration: 3000,
      position: ['x-middle', 'y-end']
    }) {
      this.message = message
      this.messageDuration = duration
      this.messageBoxPosition = position
      this.isMessageVisible = true
      this.startMessageTimer()

      return () => {
        this.isMessageVisible = false
        this.resetMessageTimer()
      }
    },

    closeMessage() {
      this.isMessageVisible = false
      this.resetMessageTimer()
    },

    getListenedEvents() {
      return Object.keys(this.$listeners)
        .filter((event) => event.startsWith('player-'))
        .map((event) => {
          const playerKebabCaseEventName = event.replace('player-', '')
          const playerEventName = kebabToCamel(playerKebabCaseEventName)
          if (!EVENTS.includes(playerEventName)) {
            console.error(
              `播放器不支持该事件，请查阅官方文档并检查拼写是否正确 [%c${playerEventName}%c]`,
              'font-weight:bold', ''
            )
            return
          }
          return {
            componentEventName: event,
            playerEventName
          }
        })
    },

    bindEventHandlers() {
      const events = this.getListenedEvents()
      const handlers = events.reduce((map, { componentEventName, playerEventName }) => {
        map[playerEventName] = (...args) => {
          this.$emit(componentEventName, {
            player: this.player,
            args
          })
        }
        return map
      }, {})

      this.player.on('ready', this.handleReady)
      this.player.on('timeupdate', this.handleTimeupdate)
      this.player.on('cancelFullScreen', this.handleCancelFullScreen)
      for (const [event, handler] of Object.entries(handlers)) {
        this.player.on(event, handler)
      }

      return () => {
        this.player.off('ready', this.handleReady)
        this.player.off('timeupdate', this.handleTimeupdate)
        this.player.off('cancelFullScreen', this.handleCancelFullScreen)
        for (const [event, handler] of Object.entries(handlers)) {
          this.player.off(event, handler)
        }
      }
    },

    handleTimeupdate(...args) {
      const newMaxPlaybackTime = Math.max(this.maxPlaybackTime, this.player.getCurrentTime())
      this.$emit('update:maxPlaybackTime', newMaxPlaybackTime)
      // 禁止向前快进
      if (!this.isForwardSeekAllowed) {
        this.player.setPreviewTime(newMaxPlaybackTime + this.minAutoPlayOffset)
      }
    },

    handleReady(...args) {
      this.isPlayerReady = true
      this.hideUnusedSettingOptions()
    },

    hideUnusedSettingOptions() {
      const selector = ['cc', 'audio', 'quality']
        .map((str) => `.prism-setting-${str}`)
        .join(',')
      this.$refs.playerRef.querySelectorAll(selector)
        .forEach((el) => {
          el.style.display = 'none'
        })
    },

    // 转发 AliyunPlayer 的方法，方便在父组件中调用
    forwardMethod() {
      const forwardMethods = [
        'play', 'pause', 'mute', 'seek',
        'unmute', 'getDuration', 'getCurrentTime',
        'setPlayerSize', 'setSpeed', 'setCover', 'setProgressMarkers'
      ]

      forwardMethods.forEach((method) => {
        this[method] = (...args) => {
          if (this.player) {
            this.player[method](...args)
          } else {
            console.error('播放器暂未初始化')
          }
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep .prism-progress {
  // 增大交互区域
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    height: calc(100% + 12px);
  }
}

::v-deep .prism-progress-hover {
  height: 4px;

  .cursor-hover {
    height: 16px;
    width: 16px;
    transform: scale(1.2);
  }
}

::v-deep .duration {
  color: #fff;
}

.slot-container {
  position: absolute;
  inset: 0;
  z-index: 1;

  &:empty {
    pointer-events: none;
  }
}

.message-box {
  position: absolute;
  background: #fff;
  padding: 10px 16px;
  border-radius: 4px;
  z-index: 1001;

  &--pos-x-end {
    left: 20px;
  }

  &--pos-x-start {
    right: 20px;
  }

  &--pos-x-middle {
    left: 50%;
    transform: translateX(-50%);
  }

  &--pos-y-end {
    bottom: 60px;
  }

  &--pos-y-start {
    top: 20px;
  }

  &--pos-y-middle {
    top: 50%;
    transform: translateY(-50%);
  }
}

.message-enter-active, .message-leave-active {
  transition: opacity 0.3s ease-in-out;
}

.message-enter, .message-leave-to {
  opacity: 0;
}
</style>
