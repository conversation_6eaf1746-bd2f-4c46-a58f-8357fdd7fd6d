<template>
  <div class="app-container">
    <div class="mainbox">
      <el-tabs v-model="queryParams.type" @tab-click="changeTab">
        <el-tab-pane label="自主学习" name="1"></el-tab-pane>
        <el-tab-pane label="答题练习" name="2"></el-tab-pane>
      </el-tabs>
      <div class="filter-container">
        <el-form :model="queryParams" ref="queryForm" :inline="true" @submit.native.prevent v-show="showSearch"
                 label-width="68px">
          <el-form-item :label="queryParams.type=='1'?'课程名称' :'练习名称'" prop="name">
            <el-input v-model.trim="queryParams.name"
                      :placeholder="'请输入' + (queryParams.type==='1'?'课程名称' :'练习名称')" clearable
                      class="input-width"/>
          </el-form-item>
          <el-form-item v-if="queryParams.type=='1'" label="课程栏目" prop="classification">
            <el-select
              v-model="queryParams.classification"
              placeholder="请选择"
              style="width: 92%;"
              clearable
            >
              <el-option
                v-for="item in groupOptions"
                :key="item.dictCode"
                :label="item.dictName"
                :value="item.dictCode"
              />
            </el-select>
          </el-form-item>
        </el-form>
        <div class="flex-1"></div>
        <div>
          <div class="flex-1"></div>
          <div style="display:flex;">
            <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
            <el-button type="primary" @click="showRule">积分规则</el-button>
          </div>
        </div>
      </div>
      <div class="table-container table-fullscreen">
        <el-form>
          <el-col :span="10">
            <el-form-item label="今日总积分:" label-width="120px">
              <el-progress :percentage="(todayGet/todayCanGet) * 100 " :stroke-width="20"
                           style="width: 80%;margin-top: 10px" :format="formatTodatScore"></el-progress>
            </el-form-item>
          </el-col>


          <div class="table-opt-container">
            <div class="flex-1"></div>
            <dt-dialog-column v-model="isShowTable" :columns="showColumns" :table-ref="$refs.table"
                              @queryTable="getList"/>
          </div>
          <el-table v-if="isShowTable" v-loading="loading" ref="table" border highlight-current-row
                    :header-cell-style="{ backgroundColor: '#f2f2f2'}" :data="IntegrationTaskList">
            <template slot="empty">
              <p>{{ $store.getters.dataText }}</p>
            </template>
            <el-table-column
              fixed="left"
              type="index"
              label="序号"
              width="70"
              :index="(index)=>(queryParams.pageNo - 1) * queryParams.pageSize + index + 1"
            />
            <el-table-column v-for="(item,index) in showColumns" :label="item.label" v-if="item.show"
                             show-overflow-tooltip :key="item.prop"
                             align="center" :prop="item.prop">
              <template slot-scope="scope">
              <span v-if="item.prop === 'remark'">{{
                  queryParams.type === '1' ? scope.row.courseScore + '分/完成一次视频/图文学习，限' + scope.row.times + '次' :
                    scope.row.courseScore + '分/每完成一次' + (scope.row.getType === 1 ? '及格' : '满分') + '答卷，限' + scope.row.times + '次'
                }}</span>
                <span v-else>{{ scope.row[item.prop] }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" :key="Math.random()" fixed="right" align="center"
                             class-name="small-padding fixed-width">
              <template slot-scope="scope">
                <el-button

                  size="small"
                  @click="handleDetail(scope.row)"
                >{{ queryParams.type === '1' ? '去学习' : '去练习' }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <dt-pagination
            v-show="total>0"
            :total="total"
            :page.sync="queryParams.pageNo"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
          />
        </el-form>
      </div>
      <el-dialog :visible.sync="ruleDialog" title="积分规则" width="30%">
        <div v-html="ruleContract" style="height: 80%"></div>
      </el-dialog>
    </div>
    <!-- 详情弹框 -->
    <dt-detail ref="dtDetailRef" :dt-detail-option="detailOption"/>
  </div>
</template>

<script>
import {
  listIntegrationTask,
  getIntegrationTask,
  delIntegrationTask,
  addIntegrationTask,
  updateIntegrationTask, getUserIntegrationTask, getTodayIntegration
} from "@/api/training-examination/integration/IntegrationTask";
import {getIntegrationRule} from "@/api/training-examination/integration/IntegrationRule";
import {trainingExerciseCheck} from "@/api/training-examination/my-practice";

export default {
  name: "IntegrationTask",
  data() {
    return {
      // 遮罩层
      loading: true,
      //显隐表格
      isShowTable: true,
      // 选中数组
      ids: [],
      //列显隐数组
      showColumns: [
        {prop: "name", label: "课程名称", show: true},
        {prop: "remark", label: "积分说明", show: true},
      ],
      showColumns1: [
        {prop: "name", label: "课程名称", show: true},
        {prop: "remark", label: "积分说明", show: true},
      ],
      showColumns2: [
        {prop: "name", label: "练习名称", show: true},
        {prop: "remark", label: "积分说明", show: true},
      ],
      editType: 1,
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 积分任务管理表格数据
      IntegrationTaskList: [],
      groupOptions: [],
      // 弹出层标题
      title: "",
      names: '',
      ruleContract: '',
      ruleDialog: false,
      // 是否显示弹出层
      open: false,
      todayGet: 0,
      todayCanGet: 0,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        type: '1',
        name: '',
        classification: ''
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {}
    };
  },
  computed: {
    //详情参数
    detailOption() {
      return {
        rows: [],
        data: {}
      }
    },
  },
  created() {
    this.getTodayIntegration()
    this.getList();
    this.businessDictList({dictTypeCode: 'courseGrouping'}).then((res) => {
      this.groupOptions = res.data.rows
    })
  },
  methods: {
    /** 查询积分任务管理列表 */
    getList() {
      this.loading = true;
      getUserIntegrationTask(this.queryParams).then(({data: response}) => {
        this.IntegrationTaskList = response.rows;
        this.total = response.totalRows;
        this.loading = false;
      });
    },
    getTodayIntegration() {
      getTodayIntegration(this.queryParams.type).then((response) => {
        this.todayCanGet = response.data.todayCanGet
        this.todayGet = response.data.todayGet
      })
    },
    showRule() {
      getIntegrationRule().then(res => {
        this.ruleContract = res.data.contract
        this.ruleDialog = true
      });
    },
    // 取消按钮
    cancel() {
      this.reset();
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    formatTodatScore(p) {
      return this.todayGet + '分/' + this.todayCanGet + '分'
    },
    /** 重置按钮操作 */
    handleReset() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    changeTab() {
      if (this.queryParams.type == '1') {
        this.showColumns = this.showColumns1
      } else {
        this.showColumns = this.showColumns2
      }
      this.getTodayIntegration()
      this.handleReset()
    },
    handleDetail(row) {
      if (this.queryParams.type == '1') {
        this.$router.push({path: '/trainingExamination/autonomousStudyDetail', query: {id: row.mainId}})
      } else {
        // 跳转答题练习页面
        trainingExerciseCheck({
          exerciseId:row.mainId
        }).then(() => {
          // 跳转答题练习页面
          this.$router.push({
            path: '/trainingExamination/takePractice',
            query: {
              id:row.mainId,
              exerciseName:encodeURIComponent(row.name)
            }
          })
        }).catch(() => {})
      }
    },
  }
};
</script>
<style>

.el-progress-bar {
  width: 80%;
}
</style>
