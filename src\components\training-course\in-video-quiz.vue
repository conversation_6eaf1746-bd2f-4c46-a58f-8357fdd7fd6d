<template>
  <div class="in-video-quiz-mask">
    <el-card header="答对以下题目，继续视频学习" class="in-video-quiz">
      <QuestionItem
        v-model="userAnswer"
        :question="question"
      >
        <template #default="{ isCorrect }">
          <div class="in-video-quiz-action">
            <el-button style="margin-left: auto;" type="primary" @click="handleSubmit(isCorrect)">
              提交
            </el-button>
          </div>
        </template>
      </QuestionItem>
    </el-card>
  </div>
</template>

<script>
import QuestionItem from '@/components/question-item'

export default {
  name: 'InVideoQuiz',
  components: {
    QuestionItem
  },

  props: {
    question: {
      type: Object,
      required: true,
      default: () => ({})
    }
  },

  data() {
    return {
      userAnswer: ''
    }
  },

  mounted() {},

  methods: {
    handleSubmit(isCorrect) {
      this.$emit('submit', isCorrect)
    }
  }
}
</script>

<style lang="scss" scoped>
.in-video-quiz-mask {
  --player-controlbar-height: 40px;
  --inset-bottom: calc(var(--player-controlbar-height));

  background-color: #00000085;
  backdrop-filter: blur(20px);
  position: absolute;
  inset: 0;
  display: flex;
  justify-content: center;
  align-items: center;

  .in-video-quiz {
    background-color: #fff;
    width: 80%;
    max-width: 700px;

    ::v-deep .el-card__header {
      font-weight: bold;
      font-size: 16px;
    }

    .in-video-quiz-title {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 20px;
      padding: 20px;
    }

    .in-video-quiz-action {
      display: flex;
    }

    .error-text {
      color: #f56c6c;
    }
  }
}
</style>
