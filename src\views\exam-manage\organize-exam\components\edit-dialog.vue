<template>
  <div>
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="80%"
      :before-close="handleClose"
      top="6vh"
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="100px"
        @submit.native.prevent
      >
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="考试名称" prop="examName">
              <el-input
                v-model="form.examName"
                maxlength="30"
                show-word-limit
                :disabled="disabled"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="考试类型" prop="examType">
              <el-select
                v-model="form.examType"
                style="width: 100%;"
                placeholder="请选择"
                :disabled="disabled"
                clearable
              >
                <el-option
                  v-for="item in jobTypeOption"
                  :key="item.dictCode"
                  :label="item.dictName"
                  :value="item.dictCode"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="考试方式" prop="examMode">
              <el-select
                v-model="form.examMode"
                placeholder="请选择"
                style="width: 100%;"
                :disabled="disabled"
                clearable
                @change="examModeChange"
              >
                <el-option label="线上考试" value="1" />

                <el-option label="线下考试" value="2" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="选择试卷" :prop="form.examMode == 1 ? 'paperId' : ''">
              <div style="display: flex;">
                <el-input
                  v-model="form.paperName"
                  maxlength="30"
                  show-word-limit
                  placeholder="试卷名称"
                  disabled
                />

                <el-button
                  v-if="!disabled"
                  type="primary"
                  style="margin-left: 6px;"
                  @click="showSelectPaper"
                >
                  选择试卷
                </el-button>
              </div>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="考试时间" prop="examTimeRange">
              <el-date-picker
                v-model="form.examTimeRange"
                type="datetimerange"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 100%;"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>

          <el-col v-if="form.examMode == 1" :span="8">
            <el-form-item label="限制次数" prop="limitTimes">
              <el-input-number
                v-model="form.limitTimes"
                :min="0"
                :precision="0"
                :controls="false"
                clearable
                placeholder="请输入"
                style="width: 100%;"
                class="input_number"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>

          <el-col v-if="form.examMode == 2" :span="8">
            <el-form-item label="考试地点" prop="examLocation">
              <el-input
                v-model="form.examLocation"
                maxlength="30"
                show-word-limit
                :disabled="disabled"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row v-if="form.examMode == 1" :gutter="20">
          <el-col :span="8">
            <el-form-item label="限时(分钟)" prop="examTime">
              <el-input-number
                v-model="form.examTime"
                :min="0"
                :precision="0"
                :controls="false"
                clearable
                placeholder="请输入"
                style="width: 100%;"
                class="input_number"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="及格线" prop="passScore">
              <el-input-number
                v-model="form.passScore"
                :min="0"
                :precision="0"
                :controls="false"
                clearable
                placeholder="请输入"
                style="width: 100%;"
                class="input_number"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="人员范围" prop="personnelScope">
              <el-select
                v-model="form.personnelScope"
                placeholder="请选择"
                style="width: 100%;"
                :disabled="disabled"
                clearable
              >
                <el-option label="公司人员" value="1" />

                <el-option label="相关方人员" value="2" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row v-if="form.examMode == 1" :gutter="20">
          <el-col :span="24">
            <el-form-item label="考试人员" prop="userIdArray">
              <personSelect
                ref="personSelect"
                v-model="userIds"
                :api="api"
                :reappear="true"
                :multiple="true"
                :tag-visible="true"
                :disabled="disabled"
                @handleSelectData="selectOperatorPerson"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row v-if="form.examMode == 2" :gutter="20">
          <el-col :span="8">
            <el-form-item label="限制次数" prop="limitTimes">
              <el-input-number
                v-model="form.limitTimes"
                :min="0"
                :precision="0"
                :controls="false"
                clearable
                placeholder="请输入"
                style="width: 100%;"
                class="input_number"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="限时(分钟)" prop="examTime">
              <el-input-number
                v-model="form.examTime"
                :min="0"
                :precision="0"
                :controls="false"
                clearable
                placeholder="请输入"
                style="width: 100%;"
                class="input_number"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="及格线" prop="passScore">
              <el-input-number
                v-model="form.passScore"
                :min="0"
                :precision="0"
                :controls="false"
                clearable
                placeholder="请输入"
                style="width: 100%;"
                class="input_number"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row v-if="form.examMode == 2" :gutter="20">
          <el-col :span="8">
            <el-form-item label="人员范围" prop="personnelScope">
              <el-select
                v-model="form.personnelScope"
                placeholder="请选择"
                style="width: 100%;"
                :disabled="disabled"
                clearable
              >
                <el-option label="公司人员" value="1" />

                <el-option label="相关方人员" value="2" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="16">
            <el-form-item label="考试人员" prop="userIdArray">
              <personSelect
                ref="personSelect"
                v-model="userIds"
                :api="api"
                :reappear="true"
                :multiple="true"
                :tag-visible="true"
                :disabled="disabled"
                @handleSelectData="selectOperatorPerson"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-form-item label="考试人员预览">
            <div v-if="!disabled" style="display: flex;justify-content: flex-end;margin-bottom: 10px;">
              <el-button type="primary" @click="handleDownloadTemplate">
                下载模板
              </el-button>

              <el-button
                :loading="importLoading"
                type="primary"
                @click="handleImport"
              >
                导入
              </el-button>
            </div>

            <el-table
              :data="userSelectedData"
              style="width: 100%"
              :header-cell-style="{ backgroundColor: '#f2f2f2' }"
              border
              max-height="400px"
            >
              <el-table-column
                prop="userName"
                label="姓名"
                align="center"
              />

              <el-table-column
                prop="userPhone"
                label="手机号"
                align="center"
              />

              <el-table-column
                prop="userDpt"
                label="部门"
                align="center"
              />
            </el-table>
          </el-form-item>
        </el-row>

        <el-row :gutter="20">
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="form.remark"
              type="textarea"
              maxlength="200"
              :autosize="{ minRows: 4, maxRows: 4 }"
              show-word-limit
              :disabled="disabled"
            />
          </el-form-item>
        </el-row>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="handleClose">
          {{ disabled ? '关 闭' : '取 消' }}
        </el-button>

        <el-button
          v-if="!disabled" type="primary" size="small"
          @click="handleSubmit"
        >
          提 交
        </el-button>
      </span>
    </el-dialog>

    <!-- 选择试卷弹框 -->
    <PaperSelectDialog ref="selectPaperRef" @selectPaper="handleSelectPaper" />

    <!-- 人员导入弹框 -->
    <dtDialog
      title="考试人员导入"
      :visible.sync="fileUploadVisible"
      width="620px"
      :is-loading="uploadLoading"
      comfirm-label="提 交"
      @closeDialog="handleCloseImport"
      @comfirmBtn="importDataSave"
    >
      <el-form slot="content">
        <div class="">
          <dt-importFile
            ref="dtImportFileRef"
            :down-load-template="handleImportTemplate"
            :type="'考试人员'"
          />
        </div>
      </el-form>
    </dtDialog>
  </div>
</template>

<script>
import { getTotalScore } from '@/utils/paper'
import uniq from 'lodash/uniq'
import {
  getManagingOrgUserList,
  searchUserName,
  getUserListByUserIds,
  searchOrgName
} from '@/framework/api/affairManage/flowDetail/Config'
import {
  getTrainingExamPlanDetail,
  trainingExamPlanAdd,
  trainingExamPlanEdit,
  getTrainingExamUsersPreview,
  trainingBaseInfoTemplate,
  trainingBaseInfoImport
} from '@/api/exam-manage/organize-exam'
import {
  getTrainingExamMainPaperDetail
} from '@/api/exam-manage/paper-manage'
import PaperSelectDialog from '@/components/paper-select/index.vue'

export default {
  components: {
    PaperSelectDialog
  },

  data() {
    const validatePassScore = (_rule, value, callback) => {
      if (!value) {
        return callback('请输入及格线')
      }
      if (this.form.examMode == '1') {
        if (Number(value) > this.limitPass) {
          callback(`线上考试选择试卷后设置，及格线不能大于总分(${this.limitPass})`)
        } else {
          callback()
        }
      } else {
        callback()
      }
    }

    const validateTimeRange = (rule, value, callback) => {
      if (!value || value.length == 0) {
        return callback(new Error('请选择考试时间'))
      }

      const now = new Date() // 当前时间
      const start = new Date(value[0]) // 开始时间
      const end = new Date(value[1]) // 结束时间

      if (end < start) {
        return callback(new Error('结束时间必须晚于开始时间'))
      }

      if (this.form.examMode == '1') {
        if (start < now) {
          callback(new Error('开始时间必须晚于当前时间'))
        } else if (end < now) {
          callback(new Error('结束时间必须晚于当前时间'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }

    return {
      // 弹框
      dialogVisible: false,
      dialogTitle: '',

      // 是否可编辑
      disabled: false,

      // 表单
      form: {
        examMode:'1', // 考试方式
        examTimeRange:[], // 考试时间
        userIdArray: [] // 考试人员,
      },

      rules: {
        examName: [
          { required: true, message: '请输入考试名称', trigger: 'blur' }
        ],

        examType: [
          { required: true, message: '请选择考试类型', trigger: 'change' }
        ],

        examMode: [
          { required: true, message: '请选择考试方式', trigger: 'change' }
        ],

        paperId: [
          { required: true, message: '请选择试卷', trigger: 'change' }
        ],

        examTimeRange: [
          { required: true, message: '请选择考试时间', trigger: 'change' },
          { validator: validateTimeRange, trigger: 'change' }
        ],

        limitTimes: [
          { required: true, message: '请输入限制次数', trigger: 'blur' }
        ],

        examTime: [
          { required: true, message: '请输入限时(分钟)', trigger: 'blur' }
        ],

        passScore: [
          { required: true, validator: validatePassScore, trigger: 'blur' }
        ],

        personnelScope: [
          { required: true, message: '请选择人员范围', trigger: 'change' }
        ],

        userIdArray: [
          { required: true, message: '请选择考试人员', trigger: 'change' }
        ]
      },

      //  考试类型
      jobTypeOption: [],

      // 人员选择接口
      userIds:'',
      userSelectedData:[],
      api: {
        getManagingOrgUserList,
        searchUserName,
        getUserListByUserIds,
        searchOrgName
      },

      // 导入
      importLoading: false,
      fileUploadVisible: false,
      uploadLoading: false,
      handleImportTemplate: trainingBaseInfoTemplate,

      // 总分
      limitPass: 0
    }
  },

  methods: {
    // 初始化
    init(row) {
      if (row && row.examId) {
        getTrainingExamPlanDetail({ examId: row.examId }).then((res) => {
          this.form = res.data
          if (this.form.examBeginTime && this.form.examEndTime) {
            this.$set(this.form, 'examTimeRange', [this.form.examBeginTime, this.form.examEndTime])
          } else {
            this.$set(this.form, 'examTimeRange', [])
          }

          getTrainingExamMainPaperDetail({ id: this.form.paperId }).then((res) => {
            this.limitPass = getTotalScore(res.data)
          })

          this.userIds = this.form.userIdArray.join(',')
          this.getPersonPreview()
        })
        this.dialogTitle = this.disabled ? '组织考试详情' : '编辑组织考试'
      } else {
        this.dialogTitle = '新增组织考试'
      }

      // 考试类型--字典
      this.businessDictList({ dictTypeCode: 'examType' }).then((res) => {
        this.jobTypeOption = res.data.rows
      })
      this.dialogVisible = true
    },

    // 考试方式切换
    examModeChange() {
      this.form.examTimeRange = []
      this.form.passScore = null
      this.$refs.form.clearValidate()
    },

    // 选择试卷弹框
    showSelectPaper() {
      this.$refs.selectPaperRef.init()
    },

    // 选择试卷
    handleSelectPaper(row) {
      this.$set(this.form, 'paperName', row.paperName)
      this.$set(this.form, 'paperId', row.id)
      this.$set(this.form, 'passScore', 0)
      this.$refs.form.clearValidate('passScore')
      this.limitPass = getTotalScore(row)
    },

    // 获取考试人员预览列表
    getPersonPreview() {
      getTrainingExamUsersPreview({ userIds:this.form.userIdArray.join(',') }).then((res) => {
        this.userSelectedData = res.data || []
      })
    },

    // 选择人员
    selectOperatorPerson(val) {
      this.form.userIdArray = val.map((val) => {
        return val.userId
      })
      this.getPersonPreview()
    },

    // 下载导入模板
    handleDownloadTemplate() {
      trainingBaseInfoTemplate().then((res) => {
        const blob = new Blob([res.data], {
          type: 'application/vnd.ms-excel'
        })
        const objectUrl = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.style.display = 'none'
        a.href = objectUrl // 指定下载链接
        a.download = '考试人员模板' // 指定下载文件名
        a.click()
        URL.revokeObjectURL(a.href) // 释放URL对象
      })
    },

    // 导入
    handleImport() {
      this.importLoading = true
      this.fileUploadVisible = true
      this.$nextTick().then(() => {
        this.$refs.dtImportFileRef.fileList = []
      })
    },

    // 导入数据
    importDataSave() {
      this.uploadLoading = true
      const file = this.$refs.dtImportFileRef.fileList
      if (file.length == 0) {
        this.$dtMessage({
          title: '失败',
          message: '请选择需要上传的文件',
          type: 'error',
          duration: 2000
        })
        return
      }
      const formData = new FormData()
      // 数据
      formData.append('file', file[0].raw)
      formData.append('name', file[0].name)
      trainingBaseInfoImport(formData)
        .then(async (res) => {
          this.importLoading = false
          this.uploadLoading = false
          this.fileUploadVisible = false
          this.form.userIdArray = uniq([
            ...this.form.userIdArray,
            ...res.data.map((item) => item.id)
          ])
          this.userIds = this.form.userIdArray.join(',')
          this.getPersonPreview()
          this.$message.success('导入成功')
        })
        .catch((res) => {
          this.uploadLoading = false
        })
    },

    // 关闭导入弹窗
    handleCloseImport() {
      this.fileUploadVisible = false
      this.importLoading = false
    },

    // 关闭
    handleClose() {
      // 重置状态
      this.disabled = false
      this.userIds = ''
      this.userSelectedData = []
      this.limitPass = 0

      this.$refs.form.resetFields()
      this.form = {
        examMode:'1', // 考试方式
        examTimeRange:[], // 考试时间
        userIdArray: [] // 考试人员,
      }
      this.dialogVisible = false
    },

    // 提交
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (this.form.examTimeRange.length > 0) {
            this.form.examBeginTime = this.form.examTimeRange[0]
            this.form.examEndTime = this.form.examTimeRange[1]
          } else {
            this.form.examBeginTime = ''
            this.form.examEndTime = ''
          }

          if (!this.form.examId) {
            // 新增
            trainingExamPlanAdd(this.form).then((res) => {
              this.$message.success('添加成功')
              this.handleClose()
              this.$emit('update')
            })
          } else {
            // 编辑
            trainingExamPlanEdit(this.form).then((res) => {
              this.$message.success('编辑成功')
              this.handleClose()
              this.$emit('update')
            })
          }
        } else {
          return false
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.input_number ::v-deep .el-input__inner{
  text-align: left;
}
</style>
