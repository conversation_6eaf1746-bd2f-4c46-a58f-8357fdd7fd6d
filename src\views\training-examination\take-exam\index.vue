<template>
  <div class="app-container">
    <div v-loading="loading" class="mainbox">
      <div class="exam-title header">
        <div />

        <div>{{ decodeURIComponent($route.query.examName) }}</div>

        <div>
          倒计时：{{ formattedTime }}
        </div>
      </div>

      <div class="exam-container">
        <div v-if="examPaperSingle.length > 0" class="exam-single">
          <div class="exam-title">
            单选题（每题{{ examPaperSingle[0].score }}分）
          </div>

          <div v-for="(item,index) in examPaperSingle" :key="item.questionId" class="exam-item">
            <div class="content">
              <span>{{ (index + 1) + '、' }}</span>

              <div>{{ item.content }}</div>
            </div>

            <el-radio-group v-model="examPaperSingle[index].userAnswer" @change="handleUserAnswerCache(item)">
              <el-radio
                v-if="item.optionA"
                label="0"
              >
                <span>{{ 'A、' + item.optionA }}</span>
              </el-radio>

              <el-radio
                v-if="item.optionB"
                label="1"
              >
                <span>{{ 'B、' + item.optionB }}</span>
              </el-radio>

              <el-radio
                v-if="item.optionC"
                label="2"
              >
                <span>{{ 'C、' + item.optionC }}</span>
              </el-radio>

              <el-radio
                v-if="item.optionD"
                label="3"
              >
                <span>{{ 'D、' + item.optionD }}</span>
              </el-radio>

              <el-radio
                v-if="item.optionE"
                label="4"
              >
                <span>{{ 'E、' + item.optionE }}</span>
              </el-radio>

              <el-radio
                v-if="item.optionF"
                label="5"
              >
                <span>{{ 'F、' + item.optionF }}</span>
              </el-radio>
            </el-radio-group>
          </div>
        </div>

        <div v-if="examPaperMultiple.length > 0" class="exam-multiple">
          <div class="exam-title">
            多选题（每题{{ examPaperMultiple[0].score }}分）
          </div>

          <div v-for="(item,index) in examPaperMultiple" :key="item.questionId" class="exam-item">
            <div class="content">
              <span>{{ (examPaperSingle.length + index + 1) + '、' }}</span>

              <div>{{ item.content }}</div>
            </div>

            <el-checkbox-group v-model="examPaperMultiple[index].userAnswer" @change="handleUserAnswerCache(item)">
              <el-checkbox
                v-if="item.optionA"
                label="0"
              >
                <span>{{ 'A、' + item.optionA }}</span>
              </el-checkbox>

              <el-checkbox
                v-if="item.optionB"
                label="1"
              >
                <span>{{ 'B、' + item.optionB }}</span>
              </el-checkbox>

              <el-checkbox
                v-if="item.optionC"
                label="2"
              >
                <span>{{ 'C、' + item.optionC }}</span>
              </el-checkbox>

              <el-checkbox
                v-if="item.optionD"
                label="3"
              >
                <span>{{ 'D、' + item.optionD }}</span>
              </el-checkbox>

              <el-checkbox
                v-if="item.optionE"
                label="4"
              >
                <span>{{ 'E、' + item.optionE }}</span>
              </el-checkbox>

              <el-checkbox
                v-if="item.optionF"
                label="5"
              >
                <span>{{ 'F、' + item.optionF }}</span>
              </el-checkbox>
            </el-checkbox-group>
          </div>
        </div>

        <div v-if="examPaperJudge.length > 0" class="exam-judge">
          <div class="exam-title">
            判断题（每题{{ examPaperJudge[0].score }}分）
          </div>

          <div v-for="(item,index) in examPaperJudge" :key="item.questionId" class="exam-item">
            <div class="content">
              <span>{{ (examPaperSingle.length + examPaperMultiple.length + index + 1) + '、' }}</span>

              <div>{{ item.content }}</div>
            </div>

            <el-radio-group v-model="examPaperJudge[index].userAnswer" @change="handleUserAnswerCache(item)">
              <el-radio label="0">
                <span>正确</span>
              </el-radio>

              <el-radio label="1">
                <span>错误</span>
              </el-radio>
            </el-radio-group>
          </div>
        </div>
      </div>

      <div class="submit">
        <el-button type="primary" @click="submitPaper">
          交卷
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import moment from 'moment'
import { MessageBox } from 'element-ui'
import {
  trainingExamUserPaperAdd,
  commitExamPaperAndScore,
  userAnswerCache
} from '@/api/training-examination/my-exam.js'
import {
  getTrainingRememberDetailByTrainingId
} from '@/api/training-examination/take-training-evaluation'

export default {
  name: 'TakeExam',
  data() {
    return {
      loading:false,

      // 单选题组
      examPaperSingle:[],
      // 多选题组
      examPaperMultiple:[],
      // 判断题组
      examPaperJudge:[],

      paperId:'', // 试卷id

      //  倒计时
      endTime:'', // 考试结束时间（根据开始时间计算）
      formattedTime: '', // 格式化后的时间
      timer: null // 定时器对象
    }
  },

  mounted() {
    this.getExamPaperById()
  },

  beforeDestroy() {
    // 组件销毁前清除定时器
    if (this.timer) {
      clearInterval(this.timer)
    }
  },

  methods: {
    // 初始化倒计时
    initializeCountdown(startTime, examTime) {
      this.endTime = moment(startTime).add(examTime, 'minutes').format('YYYY-MM-DD HH:mm:ss')
      this.startCountdown()
    },

    // 考试倒计时
    startCountdown() {
      // 每秒更新一次倒计时
      this.timer = setInterval(() => {
        this.updateCountdown()
      }, 1000)

      // 立即更新一次
      this.updateCountdown()
    },

    // 倒计时更新格式化
    updateCountdown() {
      const now = moment().format('YYYY-MM-DD HH:mm:ss')
      const duration = moment.duration(moment(this.endTime).diff(moment(now)))

      // 如果考试已结束
      if (duration.asSeconds() <= 0) {
        this.formattedTime = '00 时 00 分 00 秒'
        clearInterval(this.timer)
        this.timer = null
        MessageBox.alert('您的考试时间已到!', '提示', {
          confirmButtonText: '确定',
          showClose: false,
          closeOnClickModal: false,
          type: 'error'
        }).then(() => {
          this.$store.dispatch('tagsView/delView', this.$route)
          this.$router.go(-1)
        }).catch(() => {})
      }

      // 格式化时间
      const hours = Math.floor(duration.asHours())
      const minutes = duration.minutes()
      const seconds = duration.seconds()

      // 格式化为两位数显示
      this.formattedTime = `${hours.toString().padStart(2, '0')} 时 ${minutes.toString().padStart(2, '0')} 分 ${seconds.toString().padStart(2, '0')} 秒`
    },

    // 获取试卷详情
    getExamPaperById() {
      this.loading = true
      trainingExamUserPaperAdd({
        examPlanId:this.$route.query.id
      }).then((res) => {
        // 试卷id
        this.paperId = res.data.id
        // 考试时长
        this.initializeCountdown(res.data.startTime, res.data.examLimitTime)
        const questions = res.data.questions || []
        // 用户选择答案预留字段
        questions.map((item) => {
          if (item.quesType == '1') {
            item.userAnswer = (item.userAnswer == null || item.userAnswer == '' || item.userAnswer == undefined) ? [] : item.userAnswer.split(',')
          }
          return item
        })
        // 单选、多选、判断题过滤
        this.examPaperSingle = questions.filter((item) => item.quesType == 0)
        this.examPaperMultiple = questions.filter((item) => item.quesType == 1)
        this.examPaperJudge = questions.filter((item) => item.quesType == 2)
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },

    // 用户选择或修改答案缓存
    handleUserAnswerCache(item) {
      let userAnswer
      if (item.quesType == 1) {
        userAnswer = item.userAnswer.length > 0 ? item.userAnswer.sort().toString() : ''
      } else {
        userAnswer = item.userAnswer
      }
      const data = {
        id:this.paperId,
        examPlanId:this.$route.query.id,
        questions:[
          {
            questionId:item.questionId,
            userAnswer
          }
        ]
      }

      userAnswerCache(data).then((res) => {})
    },

    // 提交试卷
    async submitPaper() {
      let examPaperMultiple = JSON.parse(JSON.stringify(this.examPaperMultiple))
      // 多选答案数组排序转字符串
      examPaperMultiple = examPaperMultiple.map((val) => {
        val.userAnswer.sort()
        val.userAnswer = val.userAnswer.toString()
        return val
      })
      const data = {
        id:this.paperId,
        questions:[...this.examPaperSingle, ...examPaperMultiple, ...this.examPaperJudge]
      }

      this.loading = true
      commitExamPaperAndScore(data).then((res) => {
        if (res.success == true) {
          if (!this.$route.query.trainId) {
            this.loading = false
            this.$message({
              message: '考试结束！',
              type: 'success'
            })
            this.$store.dispatch('tagsView/delView', this.$route)
            this.$router.go(-1)
          } else {
            getTrainingRememberDetailByTrainingId({ trainingId: this.$route.query.trainId }).then((res) => {
              if (res.data.evaluationStatus == '1') {
                this.loading = false
                this.$message({
                  message: '考试结束并且您已提交过培训评价！',
                  type: 'success'
                })
                this.$store.dispatch('tagsView/delView', this.$route)
                this.$router.go(-1)
              } else {
                this.loading = false
                this.$router.push({
                  path: '/trainingExamination/takeTrainingEvaluation',
                  query: {
                    trainId: this.$route.query.trainId
                  }
                })
              }
            }).catch(() => {
              this.loading = false
            })
          }
        }
      }).catch(() => {
        this.loading = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container{
  background: #fff;

  .mainbox{
    display: flex;
    flex-direction: column;
    align-items: center;

    .exam-title{
      width: 100%;
      height: 50px;
      background: #f2f2f2;
      padding: 0 20px;
      display: flex;
      align-items: center;
      color: #333;
      font-size: 16px;
      font-weight: 700;

      &.header{
        justify-content: space-between;
      }
    }

    .exam-container{
      margin-top: 20px;
      width: 96%;

      .exam-item{
        margin: 20px;

        .content{
          color: #333;
          font-size: 16px;
          display: flex;
          text-align: justify;
        }

        &:not(:last-of-type){
          border-bottom: 1px solid #eee;
        }
      }
    }

    .submit{
      margin-top: 40px;
      text-align: center;
    }
  }
}

.el-radio-group{
  display: flex;
  flex-direction: column;

  .el-radio{
    margin: 12px 0 12px 20px;
    white-space: normal;
    line-height: 20px;

  }
}

.el-checkbox-group{
  display: flex;
  flex-direction: column;

  .el-checkbox{
    margin: 12px 0 12px 20px;
    white-space: normal;
    line-height: 20px;
  }
}
</style>
