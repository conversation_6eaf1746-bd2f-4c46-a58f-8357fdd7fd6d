<template>
  <div class="question-item">
    <!-- 题干 -->
    <div class="question-title">
      <slot name="title">
        <span>{{ adaptedQuestion.title }}</span>
      </slot>
    </div>

    <div
      v-if="isChoice || isTrueOrFalse"
      class="question-option-list-wrapper"
    >
      <ol class="option-list" :class="optionListClass">
        <li
          v-for="option in adaptedQuestion.options"
          :key="option.value"
          class="option-item"
          :class="getOptionItemClass(option)"
          @click="handleOptionClick(option)"
        >
          <span class="check-marker">
            <i class="el-icon-check" />
          </span>

          <span>
            {{ option.label }}
          </span>
        </li>
      </ol>
    </div>

    <slot :user-answer="value" :is-correct="isCorrect" />
  </div>
</template>

<script>
import { CONSTANTS } from '@/constants/question'
import { adapt } from './adapter'

export default {
  name: 'QuestionItem',
  inheritAttrs: false,
  props: {
    question: {
      type: Object,
      required: true
    },

    showAnalysis: {
      type: Boolean,
      default: false
    },

    layout: {
      type: String,
      default: 'vertical',
      validator(val) {
        return ['vertical', 'horizontal'].includes(val)
      }
    },

    value: {
      type: [Array, String, Boolean, Number],
      default: () => []
    }
  },

  data() {
    return {
      CONSTANTS,
      adaptedQuestion: {
        title: '',
        options: [],
        answer: [],
        remark: ''
      }
    }
  },

  computed: {
    optionListClass() {
      return [
        `option-list-${this.layout}`,
        {
          'option-list-choice': this.isChoice,
          'option-list-true-or-false': this.isTrueOrFalse
        }
      ]
    },

    isChoice() {
      return this.adaptedQuestion.type === CONSTANTS.QUESTION_TYPE_SINGLE_CHOICE
        || this.adaptedQuestion.type === CONSTANTS.QUESTION_TYPE_MULTIPLE_CHOICE
    },

    isTrueOrFalse() {
      return this.adaptedQuestion.type === CONSTANTS.QUESTION_TYPE_TRUE_OR_FALSE
    },

    isCorrect() {
      return this.validateUserAnswer()
    }
  },

  watch: {
    question: {
      handler(val) {
        this.adaptedQuestion = adapt(val)
      },

      immediate: true
    }
  },

  methods: {
    getOptionItemClass(option) {
      let checked = false
      switch (this.adaptedQuestion.type) {
        case CONSTANTS.QUESTION_TYPE_SINGLE_CHOICE:
          checked = this.value === option.value
          break
        case CONSTANTS.QUESTION_TYPE_MULTIPLE_CHOICE:
          checked = this.value.includes(option.value)
          break
        case CONSTANTS.QUESTION_TYPE_TRUE_OR_FALSE:
          checked = this.value === option.value
          break
      }
      return checked ? 'option-item-checked' : ''
    },

    handleOptionClick(option) {
      let newValue
      switch (this.adaptedQuestion.type) {
        case CONSTANTS.QUESTION_TYPE_SINGLE_CHOICE:
          newValue = option.value
          break
        case CONSTANTS.QUESTION_TYPE_MULTIPLE_CHOICE:
          newValue = this.value.includes(option.value)
            ? this.value.filter((item) => item !== option.value)
            : [...this.value, option.value]
          break
        case CONSTANTS.QUESTION_TYPE_TRUE_OR_FALSE:
          newValue = option.value
          break
        default:
          console.error('不支持的题型')
          break
      }

      this.$emit('input', newValue)
      this.$emit('answered', newValue, this.validateUserAnswer())
    },

    validateUserAnswer() {
      let isCorrect = false
      switch (this.adaptedQuestion.type) {
        case CONSTANTS.QUESTION_TYPE_SINGLE_CHOICE:
          isCorrect = this.value === this.adaptedQuestion.answer
          break
        case CONSTANTS.QUESTION_TYPE_MULTIPLE_CHOICE:
          // 多选题需要答案完全匹配
          isCorrect = this.value.length === this.adaptedQuestion.answer.length &&
            this.value.every((v) => this.adaptedQuestion.answer.includes(v))
          break
        case CONSTANTS.QUESTION_TYPE_TRUE_OR_FALSE:
          isCorrect = this.value === this.adaptedQuestion.answer
          break
        default:
          console.error('不支持的题型')
          break
      }
      return isCorrect
    },

    getAnswerObject() {
      let result = null
      const options = this.adaptedQuestion.options
      switch (this.adaptedQuestion.type) {
        case CONSTANTS.QUESTION_TYPE_SINGLE_CHOICE:
          result = options.find((option) => option.value === this.value)
          break
        case CONSTANTS.QUESTION_TYPE_MULTIPLE_CHOICE:
          result = options.filter((option) => this.value.includes(option.value))
          break
        case CONSTANTS.QUESTION_TYPE_TRUE_OR_FALSE:
          result = options.find((option) => option.value === this.value)
          break
        default:
          console.error('不支持的题型')
          break
      }

      return result
    }


  }
}
</script>

<style lang="scss" scoped>
.question-item {
  font-size: 16px;


  .option-list {
    display: flex;
    flex-wrap: wrap;
    list-style: none;
    padding-left: 0;

    .option-item {
      position: relative;
      cursor: pointer;
      width: fit-content;
      padding: 0 10px 0 calc(1.2em + 10px);
      height: 1.8;
      line-height: 1.8em;

      &:hover {
        background-color: #f5f7fa;
      }

      .check-marker {
        position: absolute;
        left: 0px;
        color: #09bb07;
        display: none;

        .el-icon-check {
          font-weight: bold;
        }
      }
    }

    .option-item-checked {
      .check-marker {
        display: inline;
      }
    }
  }

  .option-list-choice {
    list-style-position: inside;
    list-style-type: upper-alpha;
  }


  .option-list-horizontal {
    flex-direction: row;
  }

  .option-list-vertical {
    flex-direction: column;
  }
}
</style>
